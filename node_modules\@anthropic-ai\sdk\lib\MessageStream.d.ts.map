{"version": 3, "file": "MessageStream.d.ts", "sourceRoot": "", "sources": ["../src/lib/MessageStream.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5E,OAAO,EACL,KAAK,YAAY,EACjB,QAAQ,EACR,KAAK,OAAO,EACZ,KAAK,kBAAkB,EACvB,KAAK,YAAY,EACjB,KAAK,mBAAmB,EACxB,KAAK,uBAAuB,EAE5B,KAAK,YAAY,EAClB,MAAM,sCAAsC,CAAC;AAC9C,OAAO,EAAE,KAAK,cAAc,EAAE,KAAK,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AAIpF,MAAM,WAAW,mBAAmB;IAClC,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,WAAW,EAAE,CAAC,KAAK,EAAE,kBAAkB,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;IACpE,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,KAAK,IAAI,CAAC;IACxD,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,YAAY,EAAE,KAAK,IAAI,CAAC;IAC9E,SAAS,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,CAAC;IAChE,QAAQ,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,KAAK,IAAI,CAAC;IACpE,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,CAAC;IACvC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IACpC,YAAY,EAAE,CAAC,OAAO,EAAE,YAAY,KAAK,IAAI,CAAC;IAC9C,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IACzC,KAAK,EAAE,CAAC,KAAK,EAAE,cAAc,KAAK,IAAI,CAAC;IACvC,KAAK,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC1C,GAAG,EAAE,MAAM,IAAI,CAAC;CACjB;AASD,qBAAa,aAAc,YAAW,aAAa,CAAC,kBAAkB,CAAC;;IACrE,QAAQ,EAAE,YAAY,EAAE,CAAM;IAC9B,gBAAgB,EAAE,OAAO,EAAE,CAAM;IAGjC,UAAU,EAAE,eAAe,CAAyB;;IAsCpD,IAAI,QAAQ,IAAI,QAAQ,GAAG,IAAI,GAAG,SAAS,CAE1C;IAED,IAAI,UAAU,IAAI,MAAM,GAAG,IAAI,GAAG,SAAS,CAE1C;IAED;;;;;;;;;OASG;IACG,YAAY,IAAI,OAAO,CAAC;QAC5B,IAAI,EAAE,aAAa,CAAC;QACpB,QAAQ,EAAE,QAAQ,CAAC;QACnB,UAAU,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;KACvC,CAAC;IAaF;;;;;;OAMG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,GAAG,aAAa;IAMhE,MAAM,CAAC,aAAa,CAClB,QAAQ,EAAE,QAAQ,EAClB,MAAM,EAAE,uBAAuB,EAC/B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,aAAa;IAehB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC;IAO3C,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY;IAIhD,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,UAAO;cAOnC,cAAc,CAC5B,QAAQ,EAAE,QAAQ,EAClB,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,IAAI,CAAC;IAoBhB,SAAS,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAQ9C,IAAI,KAAK,IAAI,OAAO,CAEnB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,KAAK;IAIL;;;;;;OAMG;IACH,EAAE,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI;IAOrG;;;;;;OAMG;IACH,GAAG,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI;IAQtG;;;;OAIG;IACH,IAAI,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI;IAOvG;;;;;;;;;;OAUG;IACH,OAAO,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAC7C,KAAK,EAAE,KAAK,GACX,OAAO,CACR,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,GAClE,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,GAAG,IAAI,GACxD,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CACzC;IAQK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAK3B,IAAI,cAAc,IAAI,OAAO,GAAG,SAAS,CAExC;IASD;;;OAGG;IACG,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;IAmBtC;;;;OAIG;IACG,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IA0BlC,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,MAAM,mBAAmB,EACrD,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IA8CjD,SAAS,CAAC,UAAU;cAqFJ,mBAAmB,CACjC,cAAc,EAAE,cAAc,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,IAAI,CAAC;IA4GhB,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,kBAAkB,CAAC;IA6D3D,gBAAgB,IAAI,cAAc;CAInC"}