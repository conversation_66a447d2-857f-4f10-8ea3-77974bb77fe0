"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/anthropic/edge/index.ts
var edge_exports = {};
__export(edge_exports, {
  createVertexAnthropic: () => createVertexAnthropic2,
  vertexAnthropic: () => vertexAnthropic
});
module.exports = __toCommonJS(edge_exports);

// src/anthropic/edge/google-vertex-anthropic-provider-edge.ts
var import_provider_utils3 = require("@ai-sdk/provider-utils");

// src/edge/google-vertex-auth-edge.ts
var import_provider_utils = require("@ai-sdk/provider-utils");
var loadCredentials = async () => {
  try {
    return {
      clientEmail: (0, import_provider_utils.loadSetting)({
        settingValue: void 0,
        settingName: "clientEmail",
        environmentVariableName: "GOOGLE_CLIENT_EMAIL",
        description: "Google client email"
      }),
      privateKey: (0, import_provider_utils.loadSetting)({
        settingValue: void 0,
        settingName: "privateKey",
        environmentVariableName: "GOOGLE_PRIVATE_KEY",
        description: "Google private key"
      }),
      privateKeyId: (0, import_provider_utils.loadOptionalSetting)({
        settingValue: void 0,
        environmentVariableName: "GOOGLE_PRIVATE_KEY_ID"
      })
    };
  } catch (error) {
    throw new Error(`Failed to load Google credentials: ${error.message}`);
  }
};
var base64url = (str) => {
  return btoa(str).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
};
var importPrivateKey = async (pemKey) => {
  const pemHeader = "-----BEGIN PRIVATE KEY-----";
  const pemFooter = "-----END PRIVATE KEY-----";
  const pemContents = pemKey.replace(pemHeader, "").replace(pemFooter, "").replace(/\s/g, "");
  const binaryString = atob(pemContents);
  const binaryData = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    binaryData[i] = binaryString.charCodeAt(i);
  }
  return await crypto.subtle.importKey(
    "pkcs8",
    binaryData,
    { name: "RSASSA-PKCS1-v1_5", hash: "SHA-256" },
    true,
    ["sign"]
  );
};
var buildJwt = async (credentials) => {
  const now = Math.floor(Date.now() / 1e3);
  const header = {
    alg: "RS256",
    typ: "JWT"
  };
  if (credentials.privateKeyId) {
    header.kid = credentials.privateKeyId;
  }
  const payload = {
    iss: credentials.clientEmail,
    scope: "https://www.googleapis.com/auth/cloud-platform",
    aud: "https://oauth2.googleapis.com/token",
    exp: now + 3600,
    iat: now
  };
  const privateKey = await importPrivateKey(credentials.privateKey);
  const signingInput = `${base64url(JSON.stringify(header))}.${base64url(
    JSON.stringify(payload)
  )}`;
  const encoder = new TextEncoder();
  const data = encoder.encode(signingInput);
  const signature = await crypto.subtle.sign(
    "RSASSA-PKCS1-v1_5",
    privateKey,
    data
  );
  const signatureBase64 = base64url(
    String.fromCharCode(...new Uint8Array(signature))
  );
  return `${base64url(JSON.stringify(header))}.${base64url(
    JSON.stringify(payload)
  )}.${signatureBase64}`;
};
async function generateAuthToken(credentials) {
  try {
    const creds = credentials || await loadCredentials();
    const jwt = await buildJwt(creds);
    const response = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({
        grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
        assertion: jwt
      })
    });
    if (!response.ok) {
      throw new Error(`Token request failed: ${response.statusText}`);
    }
    const data = await response.json();
    return data.access_token;
  } catch (error) {
    throw error;
  }
}

// src/anthropic/google-vertex-anthropic-provider.ts
var import_provider = require("@ai-sdk/provider");
var import_provider_utils2 = require("@ai-sdk/provider-utils");
var import_internal = require("@ai-sdk/anthropic/internal");
function createVertexAnthropic(options = {}) {
  var _a;
  const location = (0, import_provider_utils2.loadOptionalSetting)({
    settingValue: options.location,
    environmentVariableName: "GOOGLE_VERTEX_LOCATION"
  });
  const project = (0, import_provider_utils2.loadOptionalSetting)({
    settingValue: options.project,
    environmentVariableName: "GOOGLE_VERTEX_PROJECT"
  });
  const baseURL = (_a = (0, import_provider_utils2.withoutTrailingSlash)(options.baseURL)) != null ? _a : `https://${location}-aiplatform.googleapis.com/v1/projects/${project}/locations/${location}/publishers/anthropic/models`;
  const createChatModel = (modelId, settings = {}) => {
    var _a2;
    return new import_internal.AnthropicMessagesLanguageModel(
      modelId,
      settings,
      {
        provider: "vertex.anthropic.messages",
        baseURL,
        headers: (_a2 = options.headers) != null ? _a2 : {},
        fetch: options.fetch,
        supportsImageUrls: false,
        buildRequestUrl: (baseURL2, isStreaming) => `${baseURL2}/${modelId}:${isStreaming ? "streamRawPredict" : "rawPredict"}`,
        transformRequestBody: (args) => {
          const { model, ...rest } = args;
          return {
            ...rest,
            anthropic_version: "vertex-2023-10-16"
          };
        }
      }
    );
  };
  const provider = function(modelId, settings) {
    if (new.target) {
      throw new Error(
        "The Anthropic model function cannot be called with the new keyword."
      );
    }
    return createChatModel(modelId, settings);
  };
  provider.languageModel = createChatModel;
  provider.chat = createChatModel;
  provider.messages = createChatModel;
  provider.textEmbeddingModel = (modelId) => {
    throw new import_provider.NoSuchModelError({ modelId, modelType: "textEmbeddingModel" });
  };
  provider.tools = import_internal.anthropicTools;
  return provider;
}

// src/anthropic/edge/google-vertex-anthropic-provider-edge.ts
function createVertexAnthropic2(options = {}) {
  return createVertexAnthropic({
    ...options,
    headers: async () => ({
      Authorization: `Bearer ${await generateAuthToken(
        options.googleCredentials
      )}`,
      ...await (0, import_provider_utils3.resolve)(options.headers)
    })
  });
}
var vertexAnthropic = createVertexAnthropic2();
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  createVertexAnthropic,
  vertexAnthropic
});
//# sourceMappingURL=index.js.map