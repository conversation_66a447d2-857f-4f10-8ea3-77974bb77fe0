{"version": 3, "sources": ["../src/index.ts", "../src/bedrock-provider.ts", "../src/bedrock-chat-language-model.ts", "../src/bedrock-api-types.ts", "../src/bedrock-error.ts", "../src/bedrock-event-stream-response-handler.ts", "../src/bedrock-prepare-tools.ts", "../src/convert-to-bedrock-chat-messages.ts", "../src/map-bedrock-finish-reason.ts", "../src/bedrock-embedding-model.ts", "../src/bedrock-image-model.ts", "../src/bedrock-image-settings.ts", "../src/headers-utils.ts", "../src/bedrock-sigv4-fetch.ts"], "sourcesContent": ["export { bedrock, createAmazonBedrock } from './bedrock-provider';\nexport type {\n  AmazonBedrockProvider,\n  AmazonBedrockProviderSettings,\n} from './bedrock-provider';\n", "import {\n  EmbeddingModelV1,\n  ImageModelV1,\n  LanguageModelV1,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  generateId,\n  loadOptionalSetting,\n  loadSetting,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { BedrockChatLanguageModel } from './bedrock-chat-language-model';\nimport {\n  BedrockChatModelId,\n  BedrockChatSettings,\n} from './bedrock-chat-settings';\nimport { BedrockEmbeddingModel } from './bedrock-embedding-model';\nimport {\n  BedrockEmbeddingModelId,\n  BedrockEmbeddingSettings,\n} from './bedrock-embedding-settings';\nimport { BedrockImageModel } from './bedrock-image-model';\nimport {\n  BedrockImageModelId,\n  BedrockImageSettings,\n} from './bedrock-image-settings';\nimport {\n  BedrockCredentials,\n  createSigV4FetchFunction,\n} from './bedrock-sigv4-fetch';\n\nexport interface AmazonBedrockProviderSettings {\n  /**\nThe AWS region to use for the Bedrock provider. Defaults to the value of the\n`AWS_REGION` environment variable.\n   */\n  region?: string;\n\n  /**\nThe AWS access key ID to use for the Bedrock provider. Defaults to the value of the\n`AWS_ACCESS_KEY_ID` environment variable.\n   */\n  accessKeyId?: string;\n\n  /**\nThe AWS secret access key to use for the Bedrock provider. Defaults to the value of the\n`AWS_SECRET_ACCESS_KEY` environment variable.\n   */\n  secretAccessKey?: string;\n\n  /**\nThe AWS session token to use for the Bedrock provider. Defaults to the value of the\n`AWS_SESSION_TOKEN` environment variable.\n   */\n  sessionToken?: string;\n\n  /**\nBase URL for the Bedrock API calls.\n   */\n  baseURL?: string;\n\n  /**\nCustom headers to include in the requests.\n   */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n*/\n  fetch?: FetchFunction;\n\n  /**\nThe AWS credential provider to use for the Bedrock provider to get dynamic\ncredentials similar to the AWS SDK. Setting a provider here will cause its\ncredential values to be used instead of the `accessKeyId`, `secretAccessKey`,\nand `sessionToken` settings.\n   */\n  credentialProvider?: () => PromiseLike<Omit<BedrockCredentials, 'region'>>;\n\n  // for testing\n  generateId?: () => string;\n}\n\nexport interface AmazonBedrockProvider extends ProviderV1 {\n  (\n    modelId: BedrockChatModelId,\n    settings?: BedrockChatSettings,\n  ): LanguageModelV1;\n\n  languageModel(\n    modelId: BedrockChatModelId,\n    settings?: BedrockChatSettings,\n  ): LanguageModelV1;\n\n  embedding(\n    modelId: BedrockEmbeddingModelId,\n    settings?: BedrockEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  image(\n    modelId: BedrockImageModelId,\n    settings?: BedrockImageSettings,\n  ): ImageModelV1;\n\n  imageModel(\n    modelId: BedrockImageModelId,\n    settings?: BedrockImageSettings,\n  ): ImageModelV1;\n}\n\n/**\nCreate an Amazon Bedrock provider instance.\n */\nexport function createAmazonBedrock(\n  options: AmazonBedrockProviderSettings = {},\n): AmazonBedrockProvider {\n  const sigv4Fetch = createSigV4FetchFunction(async () => {\n    const region = loadSetting({\n      settingValue: options.region,\n      settingName: 'region',\n      environmentVariableName: 'AWS_REGION',\n      description: 'AWS region',\n    });\n    // If a credential provider is provided, use it to get the credentials.\n    if (options.credentialProvider) {\n      return {\n        ...(await options.credentialProvider()),\n        region,\n      };\n    }\n    return {\n      region,\n      accessKeyId: loadSetting({\n        settingValue: options.accessKeyId,\n        settingName: 'accessKeyId',\n        environmentVariableName: 'AWS_ACCESS_KEY_ID',\n        description: 'AWS access key ID',\n      }),\n      secretAccessKey: loadSetting({\n        settingValue: options.secretAccessKey,\n        settingName: 'secretAccessKey',\n        environmentVariableName: 'AWS_SECRET_ACCESS_KEY',\n        description: 'AWS secret access key',\n      }),\n      sessionToken: loadOptionalSetting({\n        settingValue: options.sessionToken,\n        environmentVariableName: 'AWS_SESSION_TOKEN',\n      }),\n    };\n  }, options.fetch);\n\n  const getBaseUrl = (): string =>\n    withoutTrailingSlash(\n      options.baseURL ??\n        `https://bedrock-runtime.${loadSetting({\n          settingValue: options.region,\n          settingName: 'region',\n          environmentVariableName: 'AWS_REGION',\n          description: 'AWS region',\n        })}.amazonaws.com`,\n    ) ?? `https://bedrock-runtime.us-east-1.amazonaws.com`;\n\n  const createChatModel = (\n    modelId: BedrockChatModelId,\n    settings: BedrockChatSettings = {},\n  ) =>\n    new BedrockChatLanguageModel(modelId, settings, {\n      baseUrl: getBaseUrl,\n      headers: options.headers ?? {},\n      fetch: sigv4Fetch,\n      generateId,\n    });\n\n  const provider = function (\n    modelId: BedrockChatModelId,\n    settings?: BedrockChatSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Amazon Bedrock model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  const createEmbeddingModel = (\n    modelId: BedrockEmbeddingModelId,\n    settings: BedrockEmbeddingSettings = {},\n  ) =>\n    new BedrockEmbeddingModel(modelId, settings, {\n      baseUrl: getBaseUrl,\n      headers: options.headers ?? {},\n      fetch: sigv4Fetch,\n    });\n\n  const createImageModel = (\n    modelId: BedrockImageModelId,\n    settings: BedrockImageSettings = {},\n  ) =>\n    new BedrockImageModel(modelId, settings, {\n      baseUrl: getBaseUrl,\n      headers: options.headers ?? {},\n      fetch: sigv4Fetch,\n    });\n\n  provider.languageModel = createChatModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n\n  return provider;\n}\n\n/**\nDefault Bedrock provider instance.\n */\nexport const bedrock = createAmazonBedrock();\n", "import {\n  InvalidArgumentError,\n  JSONObject,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  Resolvable,\n  combineHeaders,\n  createJsonErrorResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport {\n  BEDROCK_STOP_REASONS,\n  BedrockConverseInput,\n  BedrockStopReason,\n} from './bedrock-api-types';\nimport {\n  BedrockChatModelId,\n  BedrockChatSettings,\n} from './bedrock-chat-settings';\nimport { BedrockErrorSchema } from './bedrock-error';\nimport { createBedrockEventStreamResponseHandler } from './bedrock-event-stream-response-handler';\nimport { prepareTools } from './bedrock-prepare-tools';\nimport { convertToBedrockChatMessages } from './convert-to-bedrock-chat-messages';\nimport { mapBedrockFinishReason } from './map-bedrock-finish-reason';\n\ntype BedrockChatConfig = {\n  baseUrl: () => string;\n  headers: Resolvable<Record<string, string | undefined>>;\n  fetch?: FetchFunction;\n  generateId: () => string;\n};\n\nexport class BedrockChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly provider = 'amazon-bedrock';\n  readonly defaultObjectGenerationMode = 'tool';\n  readonly supportsImageUrls = false;\n\n  constructor(\n    readonly modelId: BedrockChatModelId,\n    private readonly settings: BedrockChatSettings,\n    private readonly config: BedrockChatConfig,\n  ) {}\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]): {\n    command: BedrockConverseInput;\n    warnings: LanguageModelV1CallWarning[];\n  } {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (frequencyPenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'frequencyPenalty',\n      });\n    }\n\n    if (presencePenalty != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'presencePenalty',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (responseFormat != null && responseFormat.type !== 'text') {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'responseFormat',\n        details: 'JSON response format is not supported.',\n      });\n    }\n\n    const { system, messages } = convertToBedrockChatMessages(prompt);\n\n    // Parse thinking options from provider metadata\n    const reasoningConfigOptions =\n      BedrockReasoningConfigOptionsSchema.safeParse(\n        providerMetadata?.bedrock?.reasoning_config,\n      );\n\n    if (!reasoningConfigOptions.success) {\n      throw new InvalidArgumentError({\n        argument: 'providerOptions.bedrock.reasoning_config',\n        message: 'invalid reasoning configuration options',\n        cause: reasoningConfigOptions.error,\n      });\n    }\n\n    const isThinking = reasoningConfigOptions.data?.type === 'enabled';\n    const thinkingBudget =\n      reasoningConfigOptions.data?.budgetTokens ??\n      reasoningConfigOptions.data?.budget_tokens;\n\n    const inferenceConfig = {\n      ...(maxTokens != null && { maxTokens }),\n      ...(temperature != null && { temperature }),\n      ...(topP != null && { topP }),\n      ...(stopSequences != null && { stopSequences }),\n    };\n\n    // Adjust maxTokens if thinking is enabled\n    if (isThinking && thinkingBudget != null) {\n      if (inferenceConfig.maxTokens != null) {\n        inferenceConfig.maxTokens += thinkingBudget;\n      } else {\n        inferenceConfig.maxTokens = thinkingBudget + 4096; // Default + thinking budget maxTokens = 4096, TODO update default in v5\n      }\n      // Add them to additional model request fields\n      // Add reasoning config to additionalModelRequestFields\n      this.settings.additionalModelRequestFields = {\n        ...this.settings.additionalModelRequestFields,\n        reasoning_config: {\n          type: reasoningConfigOptions.data?.type,\n          budget_tokens: thinkingBudget,\n        },\n      };\n    }\n\n    // Remove temperature if thinking is enabled\n    if (isThinking && inferenceConfig.temperature != null) {\n      delete inferenceConfig.temperature;\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'temperature',\n        details: 'temperature is not supported when thinking is enabled',\n      });\n    }\n\n    // Remove topP if thinking is enabled\n    if (isThinking && inferenceConfig.topP != null) {\n      delete inferenceConfig.topP;\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topP',\n        details: 'topP is not supported when thinking is enabled',\n      });\n    }\n\n    const baseArgs: BedrockConverseInput = {\n      system,\n      additionalModelRequestFields: this.settings.additionalModelRequestFields,\n      ...(Object.keys(inferenceConfig).length > 0 && {\n        inferenceConfig,\n      }),\n      messages,\n      ...providerMetadata?.bedrock,\n    };\n\n    switch (type) {\n      case 'regular': {\n        const { toolConfig, toolWarnings } = prepareTools(mode);\n        return {\n          command: {\n            ...baseArgs,\n            ...(toolConfig.tools?.length ? { toolConfig } : {}),\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'json-mode object generation',\n        });\n      }\n\n      case 'object-tool': {\n        return {\n          command: {\n            ...baseArgs,\n            toolConfig: {\n              tools: [\n                {\n                  toolSpec: {\n                    name: mode.tool.name,\n                    description: mode.tool.description,\n                    inputSchema: {\n                      json: mode.tool.parameters as JSONObject,\n                    },\n                  },\n                },\n              ],\n              toolChoice: { tool: { name: mode.tool.name } },\n            },\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { command: args, warnings } = this.getArgs(options);\n\n    const url = `${this.getUrl(this.modelId)}/converse`;\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url,\n      headers: combineHeaders(\n        await resolve(this.config.headers),\n        options.headers,\n      ),\n      body: args,\n      failedResponseHandler: createJsonErrorResponseHandler({\n        errorSchema: BedrockErrorSchema,\n        errorToMessage: error => `${error.message ?? 'Unknown error'}`,\n      }),\n      successfulResponseHandler: createJsonResponseHandler(\n        BedrockResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    const providerMetadata =\n      response.trace || response.usage\n        ? {\n            bedrock: {\n              ...(response.trace && typeof response.trace === 'object'\n                ? { trace: response.trace as JSONObject }\n                : {}),\n              ...(response.usage && {\n                usage: {\n                  cacheReadInputTokens:\n                    response.usage?.cacheReadInputTokens ?? Number.NaN,\n                  cacheWriteInputTokens:\n                    response.usage?.cacheWriteInputTokens ?? Number.NaN,\n                },\n              }),\n            },\n          }\n        : undefined;\n\n    const reasoning = response.output.message.content\n      .filter(content => content.reasoningContent)\n      .map(content => {\n        if (\n          content.reasoningContent &&\n          'reasoningText' in content.reasoningContent\n        ) {\n          return {\n            type: 'text' as const,\n            text: content.reasoningContent.reasoningText.text,\n            ...(content.reasoningContent.reasoningText.signature && {\n              signature: content.reasoningContent.reasoningText.signature,\n            }),\n          };\n        } else if (\n          content.reasoningContent &&\n          'redactedReasoning' in content.reasoningContent\n        ) {\n          return {\n            type: 'redacted' as const,\n            data: content.reasoningContent.redactedReasoning.data ?? '',\n          };\n        } else {\n          // Return undefined for unexpected structures\n          return undefined;\n        }\n      })\n      // Filter out any undefined values\n      .filter((item): item is NonNullable<typeof item> => item !== undefined);\n\n    return {\n      text:\n        response.output?.message?.content\n          ?.map(part => part.text ?? '')\n          .join('') ?? undefined,\n      toolCalls: response.output?.message?.content\n        ?.filter(part => !!part.toolUse)\n        ?.map(part => ({\n          toolCallType: 'function',\n          toolCallId: part.toolUse?.toolUseId ?? this.config.generateId(),\n          toolName: part.toolUse?.name ?? `tool-${this.config.generateId()}`,\n          args: JSON.stringify(part.toolUse?.input ?? ''),\n        })),\n      finishReason: mapBedrockFinishReason(\n        response.stopReason as BedrockStopReason,\n      ),\n      usage: {\n        promptTokens: response.usage?.inputTokens ?? Number.NaN,\n        completionTokens: response.usage?.outputTokens ?? Number.NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      reasoning: reasoning.length > 0 ? reasoning : undefined,\n      ...(providerMetadata && { providerMetadata }),\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { command: args, warnings } = this.getArgs(options);\n    const url = `${this.getUrl(this.modelId)}/converse-stream`;\n\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url,\n      headers: combineHeaders(\n        await resolve(this.config.headers),\n        options.headers,\n      ),\n      body: args,\n      failedResponseHandler: createJsonErrorResponseHandler({\n        errorSchema: BedrockErrorSchema,\n        errorToMessage: error => `${error.type}: ${error.message}`,\n      }),\n      successfulResponseHandler:\n        createBedrockEventStreamResponseHandler(BedrockStreamSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    let providerMetadata: LanguageModelV1ProviderMetadata | undefined =\n      undefined;\n\n    const toolCallContentBlocks: Record<\n      number,\n      {\n        toolCallId: string;\n        toolName: string;\n        jsonText: string;\n      }\n    > = {};\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof BedrockStreamSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            function enqueueError(bedrockError: Record<string, any>) {\n              finishReason = 'error';\n              controller.enqueue({ type: 'error', error: bedrockError });\n            }\n\n            // handle failed chunk parsing / validation:\n            if (!chunk.success) {\n              enqueueError(chunk.error);\n              return;\n            }\n\n            const value = chunk.value;\n\n            // handle errors:\n            if (value.internalServerException) {\n              enqueueError(value.internalServerException);\n              return;\n            }\n            if (value.modelStreamErrorException) {\n              enqueueError(value.modelStreamErrorException);\n              return;\n            }\n            if (value.throttlingException) {\n              enqueueError(value.throttlingException);\n              return;\n            }\n            if (value.validationException) {\n              enqueueError(value.validationException);\n              return;\n            }\n\n            if (value.messageStop) {\n              finishReason = mapBedrockFinishReason(\n                value.messageStop.stopReason as BedrockStopReason,\n              );\n            }\n\n            if (value.metadata) {\n              usage = {\n                promptTokens: value.metadata.usage?.inputTokens ?? Number.NaN,\n                completionTokens:\n                  value.metadata.usage?.outputTokens ?? Number.NaN,\n              };\n\n              const cacheUsage =\n                value.metadata.usage?.cacheReadInputTokens != null ||\n                value.metadata.usage?.cacheWriteInputTokens != null\n                  ? {\n                      usage: {\n                        cacheReadInputTokens:\n                          value.metadata.usage?.cacheReadInputTokens ??\n                          Number.NaN,\n                        cacheWriteInputTokens:\n                          value.metadata.usage?.cacheWriteInputTokens ??\n                          Number.NaN,\n                      },\n                    }\n                  : undefined;\n\n              const trace = value.metadata.trace\n                ? {\n                    trace: value.metadata.trace as JSONObject,\n                  }\n                : undefined;\n\n              if (cacheUsage || trace) {\n                providerMetadata = {\n                  bedrock: {\n                    ...cacheUsage,\n                    ...trace,\n                  },\n                };\n              }\n            }\n\n            if (\n              value.contentBlockDelta?.delta &&\n              'text' in value.contentBlockDelta.delta &&\n              value.contentBlockDelta.delta.text\n            ) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: value.contentBlockDelta.delta.text,\n              });\n            }\n\n            if (\n              value.contentBlockDelta?.delta &&\n              'reasoningContent' in value.contentBlockDelta.delta &&\n              value.contentBlockDelta.delta.reasoningContent\n            ) {\n              const reasoningContent =\n                value.contentBlockDelta.delta.reasoningContent;\n              if ('text' in reasoningContent && reasoningContent.text) {\n                controller.enqueue({\n                  type: 'reasoning',\n                  textDelta: reasoningContent.text,\n                });\n              } else if (\n                'signature' in reasoningContent &&\n                reasoningContent.signature\n              ) {\n                controller.enqueue({\n                  type: 'reasoning-signature',\n                  signature: reasoningContent.signature,\n                });\n              } else if ('data' in reasoningContent && reasoningContent.data) {\n                controller.enqueue({\n                  type: 'redacted-reasoning',\n                  data: reasoningContent.data,\n                });\n              }\n            }\n\n            const contentBlockStart = value.contentBlockStart;\n            if (contentBlockStart?.start?.toolUse != null) {\n              const toolUse = contentBlockStart.start.toolUse;\n              toolCallContentBlocks[contentBlockStart.contentBlockIndex!] = {\n                toolCallId: toolUse.toolUseId!,\n                toolName: toolUse.name!,\n                jsonText: '',\n              };\n            }\n\n            const contentBlockDelta = value.contentBlockDelta;\n            if (\n              contentBlockDelta?.delta &&\n              'toolUse' in contentBlockDelta.delta &&\n              contentBlockDelta.delta.toolUse\n            ) {\n              const contentBlock =\n                toolCallContentBlocks[contentBlockDelta.contentBlockIndex!];\n              const delta = contentBlockDelta.delta.toolUse.input ?? '';\n\n              controller.enqueue({\n                type: 'tool-call-delta',\n                toolCallType: 'function',\n                toolCallId: contentBlock.toolCallId,\n                toolName: contentBlock.toolName,\n                argsTextDelta: delta,\n              });\n\n              contentBlock.jsonText += delta;\n            }\n\n            const contentBlockStop = value.contentBlockStop;\n            if (contentBlockStop != null) {\n              const index = contentBlockStop.contentBlockIndex!;\n              const contentBlock = toolCallContentBlocks[index];\n\n              // when finishing a tool call block, send the full tool call:\n              if (contentBlock != null) {\n                controller.enqueue({\n                  type: 'tool-call',\n                  toolCallType: 'function',\n                  toolCallId: contentBlock.toolCallId,\n                  toolName: contentBlock.toolName,\n                  args: contentBlock.jsonText,\n                });\n\n                delete toolCallContentBlocks[index];\n              }\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage,\n              ...(providerMetadata && { providerMetadata }),\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n    };\n  }\n\n  private getUrl(modelId: string) {\n    const encodedModelId = encodeURIComponent(modelId);\n    return `${this.config.baseUrl()}/model/${encodedModelId}`;\n  }\n}\n\nconst BedrockReasoningConfigOptionsSchema = z\n  .object({\n    type: z.union([z.literal('enabled'), z.literal('disabled')]).nullish(),\n    budget_tokens: z.number().nullish(),\n    budgetTokens: z.number().nullish(),\n  })\n  .nullish();\n\nconst BedrockStopReasonSchema = z.union([\n  z.enum(BEDROCK_STOP_REASONS),\n  z.string(),\n]);\n\nconst BedrockToolUseSchema = z.object({\n  toolUseId: z.string(),\n  name: z.string(),\n  input: z.unknown(),\n});\n\nconst BedrockReasoningTextSchema = z.object({\n  signature: z.string().nullish(),\n  text: z.string(),\n});\n\nconst BedrockRedactedReasoningSchema = z.object({\n  data: z.string(),\n});\n\n// limited version of the schema, focused on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst BedrockResponseSchema = z.object({\n  metrics: z\n    .object({\n      latencyMs: z.number(),\n    })\n    .nullish(),\n  output: z.object({\n    message: z.object({\n      content: z.array(\n        z.object({\n          text: z.string().nullish(),\n          toolUse: BedrockToolUseSchema.nullish(),\n          reasoningContent: z\n            .union([\n              z.object({\n                reasoningText: BedrockReasoningTextSchema,\n              }),\n              z.object({\n                redactedReasoning: BedrockRedactedReasoningSchema,\n              }),\n            ])\n            .nullish(),\n        }),\n      ),\n      role: z.string(),\n    }),\n  }),\n  stopReason: BedrockStopReasonSchema,\n  trace: z.unknown().nullish(),\n  usage: z.object({\n    inputTokens: z.number(),\n    outputTokens: z.number(),\n    totalTokens: z.number(),\n    cacheReadInputTokens: z.number().nullish(),\n    cacheWriteInputTokens: z.number().nullish(),\n  }),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst BedrockStreamSchema = z.object({\n  contentBlockDelta: z\n    .object({\n      contentBlockIndex: z.number(),\n      delta: z\n        .union([\n          z.object({ text: z.string() }),\n          z.object({ toolUse: z.object({ input: z.string() }) }),\n          z.object({\n            reasoningContent: z.object({ text: z.string() }),\n          }),\n          z.object({\n            reasoningContent: z.object({\n              signature: z.string(),\n            }),\n          }),\n          z.object({\n            reasoningContent: z.object({ data: z.string() }),\n          }),\n        ])\n        .nullish(),\n    })\n    .nullish(),\n  contentBlockStart: z\n    .object({\n      contentBlockIndex: z.number(),\n      start: z\n        .object({\n          toolUse: BedrockToolUseSchema.nullish(),\n        })\n        .nullish(),\n    })\n    .nullish(),\n  contentBlockStop: z\n    .object({\n      contentBlockIndex: z.number(),\n    })\n    .nullish(),\n  internalServerException: z.record(z.unknown()).nullish(),\n  messageStop: z\n    .object({\n      additionalModelResponseFields: z.record(z.unknown()).nullish(),\n      stopReason: BedrockStopReasonSchema,\n    })\n    .nullish(),\n  metadata: z\n    .object({\n      trace: z.unknown().nullish(),\n      usage: z\n        .object({\n          cacheReadInputTokens: z.number().nullish(),\n          cacheWriteInputTokens: z.number().nullish(),\n          inputTokens: z.number(),\n          outputTokens: z.number(),\n        })\n        .nullish(),\n    })\n    .nullish(),\n  modelStreamErrorException: z.record(z.unknown()).nullish(),\n  throttlingException: z.record(z.unknown()).nullish(),\n  validationException: z.record(z.unknown()).nullish(),\n});\n", "import { JSONObject } from '@ai-sdk/provider';\n\nexport interface BedrockConverseInput {\n  system?: BedrockSystemMessages;\n  messages: BedrockMessages;\n  toolConfig?: BedrockToolConfiguration;\n  inferenceConfig?: {\n    maxTokens?: number;\n    temperature?: number;\n    topP?: number;\n    stopSequences?: string[];\n  };\n  additionalModelRequestFields?: Record<string, unknown>;\n  guardrailConfig?:\n    | BedrockGuardrailConfiguration\n    | BedrockGuardrailStreamConfiguration\n    | undefined;\n}\n\nexport type BedrockSystemMessages = Array<BedrockSystemContentBlock>;\n\nexport type BedrockMessages = Array<\n  BedrockAssistantMessage | BedrockUserMessage\n>;\n\nexport interface BedrockAssistantMessage {\n  role: 'assistant';\n  content: Array<BedrockContentBlock>;\n}\n\nexport interface BedrockUserMessage {\n  role: 'user';\n  content: Array<BedrockContentBlock>;\n}\n\nexport const BEDROCK_CACHE_POINT = {\n  cachePoint: { type: 'default' },\n} as const;\n\nexport type BedrockCachePoint = { cachePoint: { type: 'default' } };\nexport type BedrockSystemContentBlock = { text: string } | BedrockCachePoint;\n\nexport interface BedrockGuardrailConfiguration {\n  guardrails?: Array<{\n    name: string;\n    description?: string;\n    parameters?: Record<string, unknown>;\n  }>;\n}\n\nexport type BedrockGuardrailStreamConfiguration = BedrockGuardrailConfiguration;\n\nexport interface BedrockToolInputSchema {\n  json: Record<string, unknown>;\n}\n\nexport interface BedrockTool {\n  toolSpec: {\n    name: string;\n    description?: string;\n    inputSchema: { json: JSONObject };\n  };\n}\n\nexport interface BedrockToolConfiguration {\n  tools?: Array<BedrockTool | BedrockCachePoint>;\n  toolChoice?:\n    | { tool: { name: string } }\n    | { auto: {} }\n    | { any: {} }\n    | undefined;\n}\n\nexport const BEDROCK_STOP_REASONS = [\n  'stop',\n  'stop_sequence',\n  'end_turn',\n  'length',\n  'max_tokens',\n  'content-filter',\n  'content_filtered',\n  'guardrail_intervened',\n  'tool-calls',\n  'tool_use',\n] as const;\n\nexport type BedrockStopReason = (typeof BEDROCK_STOP_REASONS)[number];\n\nexport type BedrockImageFormat = 'jpeg' | 'png' | 'gif';\nexport type BedrockDocumentFormat = 'pdf' | 'txt' | 'md';\n\nexport interface BedrockDocumentBlock {\n  document: {\n    format: BedrockDocumentFormat;\n    name: string;\n    source: {\n      bytes: string;\n    };\n  };\n}\n\nexport interface BedrockGuardrailConverseContentBlock {\n  guardContent: unknown;\n}\n\nexport interface BedrockImageBlock {\n  image: {\n    format: BedrockImageFormat;\n    source: {\n      bytes: string;\n    };\n  };\n}\n\nexport interface BedrockToolResultBlock {\n  toolResult: {\n    toolUseId: string;\n    content: Array<BedrockTextBlock | BedrockImageBlock>;\n  };\n}\n\nexport interface BedrockToolUseBlock {\n  toolUse: {\n    toolUseId: string;\n    name: string;\n    input: Record<string, unknown>;\n  };\n}\n\nexport interface BedrockTextBlock {\n  text: string;\n}\n\nexport interface BedrockReasoningContentBlock {\n  reasoningContent: {\n    reasoningText: {\n      text: string;\n      signature?: string;\n    };\n  };\n}\n\nexport interface BedrockRedactedReasoningContentBlock {\n  reasoningContent: {\n    redactedReasoning: {\n      data: string;\n    };\n  };\n}\n\nexport type BedrockContentBlock =\n  | BedrockDocumentBlock\n  | BedrockGuardrailConverseContentBlock\n  | BedrockImageBlock\n  | BedrockTextBlock\n  | BedrockToolResultBlock\n  | BedrockToolUseBlock\n  | BedrockReasoningContentBlock\n  | BedrockRedactedReasoningContentBlock\n  | BedrockCachePoint;\n", "import { z } from 'zod';\n\nexport const BedrockErrorSchema = z.object({\n  message: z.string(),\n  type: z.string().nullish(),\n});\n", "import { EmptyResponseBodyError } from '@ai-sdk/provider';\nimport {\n  ParseResult,\n  safeParseJSON,\n  extractResponseHeaders,\n  ResponseHandler,\n  safeValidateTypes,\n} from '@ai-sdk/provider-utils';\nimport { EventStreamCodec } from '@smithy/eventstream-codec';\nimport { toUtf8, fromUtf8 } from '@smithy/util-utf8';\nimport { ZodSchema } from 'zod';\n\n// https://docs.aws.amazon.com/lexv2/latest/dg/event-stream-encoding.html\nexport const createBedrockEventStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response);\n\n    if (response.body == null) {\n      throw new EmptyResponseBodyError({});\n    }\n\n    const codec = new EventStreamCodec(toUtf8, fromUtf8);\n    let buffer = new Uint8Array(0);\n    const textDecoder = new TextDecoder();\n\n    return {\n      responseHeaders,\n      value: response.body.pipeThrough(\n        new TransformStream<Uint8Array, ParseResult<T>>({\n          transform(chunk, controller) {\n            // Append new chunk to buffer.\n            const newBuffer = new Uint8Array(buffer.length + chunk.length);\n            newBuffer.set(buffer);\n            newBuffer.set(chunk, buffer.length);\n            buffer = newBuffer;\n\n            // Try to decode messages from buffer.\n            while (buffer.length >= 4) {\n              // The first 4 bytes are the total length (big-endian).\n              const totalLength = new DataView(\n                buffer.buffer,\n                buffer.byteOffset,\n                buffer.byteLength,\n              ).getUint32(0, false);\n\n              // If we don't have the full message yet, wait for more chunks.\n              if (buffer.length < totalLength) {\n                break;\n              }\n\n              try {\n                // Decode exactly the sub-slice for this event.\n                const subView = buffer.subarray(0, totalLength);\n                const decoded = codec.decode(subView);\n\n                // Slice the used bytes out of the buffer, removing this message.\n                buffer = buffer.slice(totalLength);\n\n                // Process the message.\n                if (decoded.headers[':message-type']?.value === 'event') {\n                  const data = textDecoder.decode(decoded.body);\n\n                  // Wrap the data in the `:event-type` field to match the expected schema.\n                  const parsedDataResult = safeParseJSON({ text: data });\n                  if (!parsedDataResult.success) {\n                    controller.enqueue(parsedDataResult);\n                    break;\n                  }\n\n                  // The `p` field appears to be padding or some other non-functional field.\n                  delete (parsedDataResult.value as any).p;\n                  let wrappedData = {\n                    [decoded.headers[':event-type']?.value as string]:\n                      parsedDataResult.value,\n                  };\n\n                  // Re-validate with the expected schema.\n                  const validatedWrappedData = safeValidateTypes({\n                    value: wrappedData,\n                    schema: chunkSchema,\n                  });\n                  if (!validatedWrappedData.success) {\n                    controller.enqueue(validatedWrappedData);\n                  } else {\n                    controller.enqueue({\n                      success: true,\n                      value: validatedWrappedData.value,\n                      rawValue: wrappedData,\n                    });\n                  }\n                }\n              } catch (e) {\n                // If we can't decode a complete message, wait for more data\n                break;\n              }\n            }\n          },\n        }),\n      ),\n    };\n  };\n", "import {\n  J<PERSON><PERSON>Object,\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { BedrockTool, BedrockToolConfiguration } from './bedrock-api-types';\n\nexport function prepareTools(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n): {\n  toolConfig: BedrockToolConfiguration; // note: do not rename, name required by Bedrock\n  toolWarnings: LanguageModelV1CallWarning[];\n} {\n  // when the tools array is empty, change it to undefined to prevent errors:\n  const tools = mode.tools?.length ? mode.tools : undefined;\n\n  if (tools == null) {\n    return {\n      toolConfig: { tools: undefined, toolChoice: undefined },\n      toolWarnings: [],\n    };\n  }\n\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n  const bedrockTools: BedrockTool[] = [];\n\n  for (const tool of tools) {\n    if (tool.type === 'provider-defined') {\n      toolWarnings.push({ type: 'unsupported-tool', tool });\n    } else {\n      bedrockTools.push({\n        toolSpec: {\n          name: tool.name,\n          description: tool.description,\n          inputSchema: {\n            json: tool.parameters as JSONObject,\n          },\n        },\n      });\n    }\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return {\n      toolConfig: { tools: bedrockTools, toolChoice: undefined },\n      toolWarnings,\n    };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return {\n        toolConfig: { tools: bedrockTools, toolChoice: { auto: {} } },\n        toolWarnings,\n      };\n    case 'required':\n      return {\n        toolConfig: { tools: bedrockTools, toolChoice: { any: {} } },\n        toolWarnings,\n      };\n    case 'none':\n      // Bedrock does not support 'none' tool choice, so we remove the tools:\n      return {\n        toolConfig: { tools: undefined, toolChoice: undefined },\n        toolWarnings,\n      };\n    case 'tool':\n      return {\n        toolConfig: {\n          tools: bedrockTools,\n          toolChoice: { tool: { name: toolChoice.toolName } },\n        },\n        toolWarnings,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import {\n  BEDROCK_CACHE_POINT,\n  BedrockAssistantMessage,\n  BedrockCachePoint,\n  BedrockDocumentFormat,\n  BedrockImageFormat,\n  BedrockMessages,\n  BedrockSystemMessages,\n  BedrockUserMessage,\n} from './bedrock-api-types';\nimport {\n  J<PERSON>NObject,\n  LanguageModelV1Message,\n  LanguageModelV1Prompt,\n  LanguageModelV1ProviderMetadata,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  convertUint8ArrayToBase64,\n  createIdGenerator,\n} from '@ai-sdk/provider-utils';\n\nconst generateFileId = createIdGenerator({ prefix: 'file', size: 16 });\n\nfunction getCachePoint(\n  providerMetadata: LanguageModelV1ProviderMetadata | undefined,\n): BedrockCachePoint | undefined {\n  return providerMetadata?.bedrock?.cachePoint as BedrockCachePoint | undefined;\n}\n\nexport function convertToBedrockChatMessages(prompt: LanguageModelV1Prompt): {\n  system: BedrockSystemMessages;\n  messages: BedrockMessages;\n} {\n  const blocks = groupIntoBlocks(prompt);\n\n  let system: BedrockSystemMessages = [];\n  const messages: BedrockMessages = [];\n\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    const isLastBlock = i === blocks.length - 1;\n    const type = block.type;\n\n    switch (type) {\n      case 'system': {\n        if (messages.length > 0) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'Multiple system messages that are separated by user/assistant messages',\n          });\n        }\n\n        for (const message of block.messages) {\n          system.push({ text: message.content });\n          if (getCachePoint(message.providerMetadata)) {\n            system.push(BEDROCK_CACHE_POINT);\n          }\n        }\n        break;\n      }\n\n      case 'user': {\n        // combines all user and tool messages in this block into a single message:\n        const bedrockContent: BedrockUserMessage['content'] = [];\n\n        for (const message of block.messages) {\n          const { role, content, providerMetadata } = message;\n          switch (role) {\n            case 'user': {\n              for (let j = 0; j < content.length; j++) {\n                const part = content[j];\n\n                switch (part.type) {\n                  case 'text': {\n                    bedrockContent.push({\n                      text: part.text,\n                    });\n                    break;\n                  }\n                  case 'image': {\n                    if (part.image instanceof URL) {\n                      // The AI SDK automatically downloads images for user image parts with URLs\n                      throw new UnsupportedFunctionalityError({\n                        functionality: 'Image URLs in user messages',\n                      });\n                    }\n\n                    bedrockContent.push({\n                      image: {\n                        format: part.mimeType?.split(\n                          '/',\n                        )?.[1] as BedrockImageFormat,\n                        source: {\n                          bytes: convertUint8ArrayToBase64(\n                            part.image ?? (part.image as Uint8Array),\n                          ),\n                        },\n                      },\n                    });\n\n                    break;\n                  }\n                  case 'file': {\n                    if (part.data instanceof URL) {\n                      // The AI SDK automatically downloads files for user file parts with URLs\n                      throw new UnsupportedFunctionalityError({\n                        functionality: 'File URLs in user messages',\n                      });\n                    }\n\n                    bedrockContent.push({\n                      document: {\n                        format: part.mimeType?.split(\n                          '/',\n                        )?.[1] as BedrockDocumentFormat,\n                        name: generateFileId(),\n                        source: {\n                          bytes: part.data,\n                        },\n                      },\n                    });\n\n                    break;\n                  }\n                }\n              }\n\n              break;\n            }\n            case 'tool': {\n              for (let i = 0; i < content.length; i++) {\n                const part = content[i];\n                const toolResultContent =\n                  part.content != undefined\n                    ? part.content.map(part => {\n                        switch (part.type) {\n                          case 'text':\n                            return {\n                              text: part.text,\n                            };\n                          case 'image':\n                            if (!part.mimeType) {\n                              throw new Error(\n                                'Image mime type is required in tool result part content',\n                              );\n                            }\n                            const format = part.mimeType.split('/')[1];\n                            if (!isBedrockImageFormat(format)) {\n                              throw new Error(\n                                `Unsupported image format: ${format}`,\n                              );\n                            }\n                            return {\n                              image: {\n                                format,\n                                source: {\n                                  bytes: part.data,\n                                },\n                              },\n                            };\n                        }\n                      })\n                    : [{ text: JSON.stringify(part.result) }];\n\n                bedrockContent.push({\n                  toolResult: {\n                    toolUseId: part.toolCallId,\n                    content: toolResultContent,\n                  },\n                });\n              }\n\n              break;\n            }\n            default: {\n              const _exhaustiveCheck: never = role;\n              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n            }\n          }\n\n          if (getCachePoint(providerMetadata)) {\n            bedrockContent.push(BEDROCK_CACHE_POINT);\n          }\n        }\n\n        messages.push({ role: 'user', content: bedrockContent });\n\n        break;\n      }\n\n      case 'assistant': {\n        // combines multiple assistant messages in this block into a single message:\n        const bedrockContent: BedrockAssistantMessage['content'] = [];\n\n        for (let j = 0; j < block.messages.length; j++) {\n          const message = block.messages[j];\n          const isLastMessage = j === block.messages.length - 1;\n          const { content } = message;\n\n          for (let k = 0; k < content.length; k++) {\n            const part = content[k];\n            const isLastContentPart = k === content.length - 1;\n\n            switch (part.type) {\n              case 'text': {\n                bedrockContent.push({\n                  text:\n                    // trim the last text part if it's the last message in the block\n                    // because Bedrock does not allow trailing whitespace\n                    // in pre-filled assistant responses\n                    trimIfLast(\n                      isLastBlock,\n                      isLastMessage,\n                      isLastContentPart,\n                      part.text,\n                    ),\n                });\n                break;\n              }\n\n              case 'reasoning': {\n                bedrockContent.push({\n                  reasoningContent: {\n                    reasoningText: {\n                      // trim the last text part if it's the last message in the block\n                      // because Bedrock does not allow trailing whitespace\n                      // in pre-filled assistant responses\n                      text: trimIfLast(\n                        isLastBlock,\n                        isLastMessage,\n                        isLastContentPart,\n                        part.text,\n                      ),\n                      signature: part.signature,\n                    },\n                  },\n                });\n                break;\n              }\n\n              case 'redacted-reasoning': {\n                bedrockContent.push({\n                  reasoningContent: {\n                    redactedReasoning: {\n                      data: part.data,\n                    },\n                  },\n                });\n                break;\n              }\n\n              case 'tool-call': {\n                bedrockContent.push({\n                  toolUse: {\n                    toolUseId: part.toolCallId,\n                    name: part.toolName,\n                    input: part.args as JSONObject,\n                  },\n                });\n                break;\n              }\n            }\n          }\n          if (getCachePoint(message.providerMetadata)) {\n            bedrockContent.push(BEDROCK_CACHE_POINT);\n          }\n        }\n\n        messages.push({ role: 'assistant', content: bedrockContent });\n\n        break;\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return { system, messages };\n}\n\nfunction isBedrockImageFormat(format: string): format is BedrockImageFormat {\n  return ['jpeg', 'png', 'gif'].includes(format);\n}\n\nfunction trimIfLast(\n  isLastBlock: boolean,\n  isLastMessage: boolean,\n  isLastContentPart: boolean,\n  text: string,\n) {\n  return isLastBlock && isLastMessage && isLastContentPart ? text.trim() : text;\n}\n\ntype SystemBlock = {\n  type: 'system';\n  messages: Array<LanguageModelV1Message & { role: 'system' }>;\n};\ntype AssistantBlock = {\n  type: 'assistant';\n  messages: Array<LanguageModelV1Message & { role: 'assistant' }>;\n};\ntype UserBlock = {\n  type: 'user';\n  messages: Array<LanguageModelV1Message & { role: 'user' | 'tool' }>;\n};\n\nfunction groupIntoBlocks(\n  prompt: LanguageModelV1Prompt,\n): Array<SystemBlock | AssistantBlock | UserBlock> {\n  const blocks: Array<SystemBlock | AssistantBlock | UserBlock> = [];\n  let currentBlock: SystemBlock | AssistantBlock | UserBlock | undefined =\n    undefined;\n\n  for (const message of prompt) {\n    const { role } = message;\n    switch (role) {\n      case 'system': {\n        if (currentBlock?.type !== 'system') {\n          currentBlock = { type: 'system', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'assistant': {\n        if (currentBlock?.type !== 'assistant') {\n          currentBlock = { type: 'assistant', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'user': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      case 'tool': {\n        if (currentBlock?.type !== 'user') {\n          currentBlock = { type: 'user', messages: [] };\n          blocks.push(currentBlock);\n        }\n\n        currentBlock.messages.push(message);\n        break;\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return blocks;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\nimport { BedrockStopReason } from './bedrock-api-types';\n\nexport function mapBedrockFinishReason(\n  finishReason?: BedrockStopReason,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop_sequence':\n    case 'end_turn':\n      return 'stop';\n    case 'max_tokens':\n      return 'length';\n    case 'content_filtered':\n    case 'guardrail_intervened':\n      return 'content-filter';\n    case 'tool_use':\n      return 'tool-calls';\n    default:\n      return 'unknown';\n  }\n}\n", "import { EmbeddingModelV1, EmbeddingModelV1Embedding } from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  Resolvable,\n  combineHeaders,\n  createJsonErrorResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport {\n  BedrockEmbeddingModelId,\n  BedrockEmbeddingSettings,\n} from './bedrock-embedding-settings';\nimport { BedrockErrorSchema } from './bedrock-error';\nimport { z } from 'zod';\n\ntype BedrockEmbeddingConfig = {\n  baseUrl: () => string;\n  headers: Resolvable<Record<string, string | undefined>>;\n  fetch?: FetchFunction;\n};\n\ntype DoEmbedResponse = Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>;\n\nexport class BedrockEmbeddingModel implements EmbeddingModelV1<string> {\n  readonly specificationVersion = 'v1';\n  readonly provider = 'amazon-bedrock';\n  readonly maxEmbeddingsPerCall = undefined;\n  readonly supportsParallelCalls = true;\n\n  constructor(\n    readonly modelId: BedrockEmbeddingModelId,\n    private readonly settings: BedrockEmbeddingSettings,\n    private readonly config: BedrockEmbeddingConfig,\n  ) {}\n\n  private getUrl(modelId: string): string {\n    const encodedModelId = encodeURIComponent(modelId);\n    return `${this.config.baseUrl()}/model/${encodedModelId}/invoke`;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n  }: Parameters<\n    EmbeddingModelV1<string>['doEmbed']\n  >[0]): Promise<DoEmbedResponse> {\n    const embedSingleText = async (inputText: string) => {\n      // https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_InvokeModel.html\n      const args = {\n        inputText,\n        dimensions: this.settings.dimensions,\n        normalize: this.settings.normalize,\n      };\n      const url = this.getUrl(this.modelId);\n      const { value: response } = await postJsonToApi({\n        url,\n        headers: await resolve(\n          combineHeaders(await resolve(this.config.headers), headers),\n        ),\n        body: args,\n        failedResponseHandler: createJsonErrorResponseHandler({\n          errorSchema: BedrockErrorSchema,\n          errorToMessage: error => `${error.type}: ${error.message}`,\n        }),\n        successfulResponseHandler: createJsonResponseHandler(\n          BedrockEmbeddingResponseSchema,\n        ),\n        fetch: this.config.fetch,\n        abortSignal,\n      });\n\n      return {\n        embedding: response.embedding,\n        inputTextTokenCount: response.inputTextTokenCount,\n      };\n    };\n\n    const responses = await Promise.all(values.map(embedSingleText));\n    return responses.reduce<{\n      embeddings: EmbeddingModelV1Embedding[];\n      usage: { tokens: number };\n    }>(\n      (accumulated, response) => {\n        accumulated.embeddings.push(response.embedding);\n        accumulated.usage.tokens += response.inputTextTokenCount;\n        return accumulated;\n      },\n      { embeddings: [], usage: { tokens: 0 } },\n    );\n  }\n}\n\nconst BedrockEmbeddingResponseSchema = z.object({\n  embedding: z.array(z.number()),\n  inputTextTokenCount: z.number(),\n});\n", "import { ImageModelV1, ImageModelV1CallWarning } from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  Resolvable,\n  combineHeaders,\n  createJsonErrorResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport {\n  BedrockImageModelId,\n  BedrockImageSettings,\n  modelMaxImagesPerCall,\n} from './bedrock-image-settings';\nimport { BedrockErrorSchema } from './bedrock-error';\nimport { z } from 'zod';\n\ntype BedrockImageModelConfig = {\n  baseUrl: () => string;\n  headers: Resolvable<Record<string, string | undefined>>;\n  fetch?: FetchFunction;\n  _internal?: {\n    currentDate?: () => Date;\n  };\n};\n\nexport class BedrockImageModel implements ImageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly provider = 'amazon-bedrock';\n\n  get maxImagesPerCall(): number {\n    return (\n      this.settings.maxImagesPerCall ?? modelMaxImagesPerCall[this.modelId] ?? 1\n    );\n  }\n\n  private getUrl(modelId: string): string {\n    const encodedModelId = encodeURIComponent(modelId);\n    return `${this.config.baseUrl()}/model/${encodedModelId}/invoke`;\n  }\n\n  constructor(\n    readonly modelId: BedrockImageModelId,\n    private readonly settings: BedrockImageSettings,\n    private readonly config: BedrockImageModelConfig,\n  ) {}\n\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    aspectRatio,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal,\n  }: Parameters<ImageModelV1['doGenerate']>[0]): Promise<\n    Awaited<ReturnType<ImageModelV1['doGenerate']>>\n  > {\n    const warnings: Array<ImageModelV1CallWarning> = [];\n    const [width, height] = size ? size.split('x').map(Number) : [];\n    const args = {\n      taskType: 'TEXT_IMAGE',\n      textToImageParams: {\n        text: prompt,\n        ...(providerOptions?.bedrock?.negativeText\n          ? {\n              negativeText: providerOptions.bedrock.negativeText,\n            }\n          : {}),\n      },\n      imageGenerationConfig: {\n        ...(width ? { width } : {}),\n        ...(height ? { height } : {}),\n        ...(seed ? { seed } : {}),\n        ...(n ? { numberOfImages: n } : {}),\n        ...(providerOptions?.bedrock?.quality\n          ? { quality: providerOptions.bedrock.quality }\n          : {}),\n        ...(providerOptions?.bedrock?.cfgScale\n          ? { cfgScale: providerOptions.bedrock.cfgScale }\n          : {}),\n      },\n    };\n\n    if (aspectRatio != undefined) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'aspectRatio',\n        details:\n          'This model does not support aspect ratio. Use `size` instead.',\n      });\n    }\n\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: this.getUrl(this.modelId),\n      headers: await resolve(\n        combineHeaders(await resolve(this.config.headers), headers),\n      ),\n      body: args,\n      failedResponseHandler: createJsonErrorResponseHandler({\n        errorSchema: BedrockErrorSchema,\n        errorToMessage: error => `${error.type}: ${error.message}`,\n      }),\n      successfulResponseHandler: createJsonResponseHandler(\n        bedrockImageResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      images: response.images,\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n      },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst bedrockImageResponseSchema = z.object({\n  images: z.array(z.string()),\n});\n", "export type BedrockImageModelId = 'amazon.nova-canvas-v1:0' | (string & {});\n\n// https://docs.aws.amazon.com/nova/latest/userguide/image-gen-req-resp-structure.html\nexport const modelMaxImagesPerCall: Record<BedrockImageModelId, number> = {\n  'amazon.nova-canvas-v1:0': 5,\n};\n\nexport interface BedrockImageSettings {\n  /**\n   * Override the maximum number of images per call (default is dependent on the\n   * model, or 1 for an unknown model).\n   */\n  maxImagesPerCall?: number;\n}\n", "/**\n * Extract headers from a `HeadersInit` object and convert them to a record of\n * lowercase keys and (preserving original case) values.\n * @param headers - The `HeadersInit` object to extract headers from.\n * @returns A record of lowercase keys and (preserving original case) values.\n */\nexport function extractHeaders(\n  headers: HeadersInit | undefined,\n): Record<string, string | undefined> {\n  let originalHeaders: Record<string, string | undefined> = {};\n  if (headers) {\n    if (headers instanceof Headers) {\n      originalHeaders = convertHeadersToRecord(headers);\n    } else if (Array.isArray(headers)) {\n      for (const [k, v] of headers) {\n        originalHeaders[k.toLowerCase()] = v;\n      }\n    } else {\n      originalHeaders = Object.fromEntries(\n        Object.entries(headers).map(([k, v]) => [k.toLowerCase(), v]),\n      ) as Record<string, string>;\n    }\n  }\n  return originalHeaders;\n}\n\n/**\n * Convert a Headers object to a record of lowercase keys and (preserving\n * original case) values.\n * @param headers - The Headers object to convert.\n * @returns A record of lowercase keys and values.\n */\nexport function convertHeadersToRecord(\n  headers: Headers,\n): Record<string, string> {\n  const record: Record<string, string> = {};\n  headers.forEach((value, key) => {\n    record[key.toLowerCase()] = value;\n  });\n  return record;\n}\n", "import { convertHeadersToRecord, extractHeaders } from './headers-utils';\nimport {\n  FetchFunction,\n  combineHeaders,\n  removeUndefinedEntries,\n} from '@ai-sdk/provider-utils';\nimport { AwsV4Signer } from 'aws4fetch';\n\nexport interface BedrockCredentials {\n  region: string;\n  accessKeyId: string;\n  secretAccessKey: string;\n  sessionToken?: string;\n}\n\n/**\nCreates a fetch function that applies AWS Signature Version 4 signing.\n\n@param getCredentials - Function that returns the AWS credentials to use when signing.\n@param fetch - Optional original fetch implementation to wrap. Defaults to global fetch.\n@returns A FetchFunction that signs requests before passing them to the underlying fetch.\n */\nexport function createSigV4FetchFunction(\n  getCredentials: () => BedrockCredentials | PromiseLike<BedrockCredentials>,\n  fetch: FetchFunction = globalThis.fetch,\n): FetchFunction {\n  return async (\n    input: RequestInfo | URL,\n    init?: RequestInit,\n  ): Promise<Response> => {\n    if (init?.method?.toUpperCase() !== 'POST' || !init?.body) {\n      return fetch(input, init);\n    }\n\n    const url =\n      typeof input === 'string'\n        ? input\n        : input instanceof URL\n          ? input.href\n          : input.url;\n\n    const originalHeaders = extractHeaders(init.headers);\n    const body = prepareBodyString(init.body);\n    const credentials = await getCredentials();\n    const signer = new AwsV4Signer({\n      url,\n      method: 'POST',\n      headers: Object.entries(removeUndefinedEntries(originalHeaders)),\n      body,\n      region: credentials.region,\n      accessKeyId: credentials.accessKeyId,\n      secretAccessKey: credentials.secretAccessKey,\n      sessionToken: credentials.sessionToken,\n      service: 'bedrock',\n    });\n\n    const signingResult = await signer.sign();\n    const signedHeaders = convertHeadersToRecord(signingResult.headers);\n    return fetch(input, {\n      ...init,\n      body,\n      headers: removeUndefinedEntries(\n        combineHeaders(originalHeaders, signedHeaders),\n      ),\n    });\n  };\n}\n\nfunction prepareBodyString(body: BodyInit | undefined): string {\n  if (typeof body === 'string') {\n    return body;\n  } else if (body instanceof Uint8Array) {\n    return new TextDecoder().decode(body);\n  } else if (body instanceof ArrayBuffer) {\n    return new TextDecoder().decode(new Uint8Array(body));\n  } else {\n    return JSON.stringify(body);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACMA,IAAAA,yBAMO;;;ACZP,IAAAC,mBASO;AACP,IAAAC,yBASO;AACP,IAAAC,cAAkB;;;ACeX,IAAM,sBAAsB;AAAA,EACjC,YAAY,EAAE,MAAM,UAAU;AAChC;AAoCO,IAAM,uBAAuB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACpFA,iBAAkB;AAEX,IAAM,qBAAqB,aAAE,OAAO;AAAA,EACzC,SAAS,aAAE,OAAO;AAAA,EAClB,MAAM,aAAE,OAAO,EAAE,QAAQ;AAC3B,CAAC;;;ACLD,sBAAuC;AACvC,4BAMO;AACP,+BAAiC;AACjC,uBAAiC;AAI1B,IAAM,0CACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,sBAAkB,8CAAuB,QAAQ;AAEvD,MAAI,SAAS,QAAQ,MAAM;AACzB,UAAM,IAAI,uCAAuB,CAAC,CAAC;AAAA,EACrC;AAEA,QAAM,QAAQ,IAAI,0CAAiB,yBAAQ,yBAAQ;AACnD,MAAI,SAAS,IAAI,WAAW,CAAC;AAC7B,QAAM,cAAc,IAAI,YAAY;AAEpC,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAS,KAAK;AAAA,MACnB,IAAI,gBAA4C;AAAA,QAC9C,UAAU,OAAO,YAAY;AAhCvC;AAkCY,gBAAM,YAAY,IAAI,WAAW,OAAO,SAAS,MAAM,MAAM;AAC7D,oBAAU,IAAI,MAAM;AACpB,oBAAU,IAAI,OAAO,OAAO,MAAM;AAClC,mBAAS;AAGT,iBAAO,OAAO,UAAU,GAAG;AAEzB,kBAAM,cAAc,IAAI;AAAA,cACtB,OAAO;AAAA,cACP,OAAO;AAAA,cACP,OAAO;AAAA,YACT,EAAE,UAAU,GAAG,KAAK;AAGpB,gBAAI,OAAO,SAAS,aAAa;AAC/B;AAAA,YACF;AAEA,gBAAI;AAEF,oBAAM,UAAU,OAAO,SAAS,GAAG,WAAW;AAC9C,oBAAM,UAAU,MAAM,OAAO,OAAO;AAGpC,uBAAS,OAAO,MAAM,WAAW;AAGjC,oBAAI,aAAQ,QAAQ,eAAe,MAA/B,mBAAkC,WAAU,SAAS;AACvD,sBAAM,OAAO,YAAY,OAAO,QAAQ,IAAI;AAG5C,sBAAM,uBAAmB,qCAAc,EAAE,MAAM,KAAK,CAAC;AACrD,oBAAI,CAAC,iBAAiB,SAAS;AAC7B,6BAAW,QAAQ,gBAAgB;AACnC;AAAA,gBACF;AAGA,uBAAQ,iBAAiB,MAAc;AACvC,oBAAI,cAAc;AAAA,kBAChB,EAAC,aAAQ,QAAQ,aAAa,MAA7B,mBAAgC,KAAe,GAC9C,iBAAiB;AAAA,gBACrB;AAGA,sBAAM,2BAAuB,yCAAkB;AAAA,kBAC7C,OAAO;AAAA,kBACP,QAAQ;AAAA,gBACV,CAAC;AACD,oBAAI,CAAC,qBAAqB,SAAS;AACjC,6BAAW,QAAQ,oBAAoB;AAAA,gBACzC,OAAO;AACL,6BAAW,QAAQ;AAAA,oBACjB,SAAS;AAAA,oBACT,OAAO,qBAAqB;AAAA,oBAC5B,UAAU;AAAA,kBACZ,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF,SAAS,GAAG;AAEV;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;ACvGF,IAAAC,mBAKO;AAGA,SAAS,aACd,MAMA;AAfF;AAiBE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAEhD,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,MACL,YAAY,EAAE,OAAO,QAAW,YAAY,OAAU;AAAA,MACtD,cAAc,CAAC;AAAA,IACjB;AAAA,EACF;AAEA,QAAM,eAA6C,CAAC;AACpD,QAAM,eAA8B,CAAC;AAErC,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AAAA,IACtD,OAAO;AACL,mBAAa,KAAK;AAAA,QAChB,UAAU;AAAA,UACR,MAAM,KAAK;AAAA,UACX,aAAa,KAAK;AAAA,UAClB,aAAa;AAAA,YACX,MAAM,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO;AAAA,MACL,YAAY,EAAE,OAAO,cAAc,YAAY,OAAU;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,YAAY,EAAE,OAAO,cAAc,YAAY,EAAE,MAAM,CAAC,EAAE,EAAE;AAAA,QAC5D;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,YAAY,EAAE,OAAO,cAAc,YAAY,EAAE,KAAK,CAAC,EAAE,EAAE;AAAA,QAC3D;AAAA,MACF;AAAA,IACF,KAAK;AAEH,aAAO;AAAA,QACL,YAAY,EAAE,OAAO,QAAW,YAAY,OAAU;AAAA,QACtD;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,YAAY;AAAA,UACV,OAAO;AAAA,UACP,YAAY,EAAE,MAAM,EAAE,MAAM,WAAW,SAAS,EAAE;AAAA,QACpD;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAI,+CAA8B;AAAA,QACtC,eAAe,iCAAiC,gBAAgB;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AC9EA,IAAAC,mBAMO;AACP,IAAAC,yBAGO;AAEP,IAAM,qBAAiB,0CAAkB,EAAE,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAErE,SAAS,cACP,kBAC+B;AA1BjC;AA2BE,UAAO,0DAAkB,YAAlB,mBAA2B;AACpC;AAEO,SAAS,6BAA6B,QAG3C;AAjCF;AAkCE,QAAM,SAAS,gBAAgB,MAAM;AAErC,MAAI,SAAgC,CAAC;AACrC,QAAM,WAA4B,CAAC;AAEnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,cAAc,MAAM,OAAO,SAAS;AAC1C,UAAM,OAAO,MAAM;AAEnB,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,YAAI,SAAS,SAAS,GAAG;AACvB,gBAAM,IAAI,+CAA8B;AAAA,YACtC,eACE;AAAA,UACJ,CAAC;AAAA,QACH;AAEA,mBAAW,WAAW,MAAM,UAAU;AACpC,iBAAO,KAAK,EAAE,MAAM,QAAQ,QAAQ,CAAC;AACrC,cAAI,cAAc,QAAQ,gBAAgB,GAAG;AAC3C,mBAAO,KAAK,mBAAmB;AAAA,UACjC;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AAEX,cAAM,iBAAgD,CAAC;AAEvD,mBAAW,WAAW,MAAM,UAAU;AACpC,gBAAM,EAAE,MAAM,SAAS,iBAAiB,IAAI;AAC5C,kBAAQ,MAAM;AAAA,YACZ,KAAK,QAAQ;AACX,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,sBAAM,OAAO,QAAQ,CAAC;AAEtB,wBAAQ,KAAK,MAAM;AAAA,kBACjB,KAAK,QAAQ;AACX,mCAAe,KAAK;AAAA,sBAClB,MAAM,KAAK;AAAA,oBACb,CAAC;AACD;AAAA,kBACF;AAAA,kBACA,KAAK,SAAS;AACZ,wBAAI,KAAK,iBAAiB,KAAK;AAE7B,4BAAM,IAAI,+CAA8B;AAAA,wBACtC,eAAe;AAAA,sBACjB,CAAC;AAAA,oBACH;AAEA,mCAAe,KAAK;AAAA,sBAClB,OAAO;AAAA,wBACL,SAAQ,gBAAK,aAAL,mBAAe;AAAA,0BACrB;AAAA,8BADM,mBAEJ;AAAA,wBACJ,QAAQ;AAAA,0BACN,WAAO;AAAA,6BACL,UAAK,UAAL,YAAe,KAAK;AAAA,0BACtB;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF,CAAC;AAED;AAAA,kBACF;AAAA,kBACA,KAAK,QAAQ;AACX,wBAAI,KAAK,gBAAgB,KAAK;AAE5B,4BAAM,IAAI,+CAA8B;AAAA,wBACtC,eAAe;AAAA,sBACjB,CAAC;AAAA,oBACH;AAEA,mCAAe,KAAK;AAAA,sBAClB,UAAU;AAAA,wBACR,SAAQ,gBAAK,aAAL,mBAAe;AAAA,0BACrB;AAAA,8BADM,mBAEJ;AAAA,wBACJ,MAAM,eAAe;AAAA,wBACrB,QAAQ;AAAA,0BACN,OAAO,KAAK;AAAA,wBACd;AAAA,sBACF;AAAA,oBACF,CAAC;AAED;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAEA;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AACX,uBAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACvC,sBAAM,OAAO,QAAQA,EAAC;AACtB,sBAAM,oBACJ,KAAK,WAAW,SACZ,KAAK,QAAQ,IAAI,CAAAC,UAAQ;AACvB,0BAAQA,MAAK,MAAM;AAAA,oBACjB,KAAK;AACH,6BAAO;AAAA,wBACL,MAAMA,MAAK;AAAA,sBACb;AAAA,oBACF,KAAK;AACH,0BAAI,CAACA,MAAK,UAAU;AAClB,8BAAM,IAAI;AAAA,0BACR;AAAA,wBACF;AAAA,sBACF;AACA,4BAAM,SAASA,MAAK,SAAS,MAAM,GAAG,EAAE,CAAC;AACzC,0BAAI,CAAC,qBAAqB,MAAM,GAAG;AACjC,8BAAM,IAAI;AAAA,0BACR,6BAA6B,MAAM;AAAA,wBACrC;AAAA,sBACF;AACA,6BAAO;AAAA,wBACL,OAAO;AAAA,0BACL;AAAA,0BACA,QAAQ;AAAA,4BACN,OAAOA,MAAK;AAAA,0BACd;AAAA,wBACF;AAAA,sBACF;AAAA,kBACJ;AAAA,gBACF,CAAC,IACD,CAAC,EAAE,MAAM,KAAK,UAAU,KAAK,MAAM,EAAE,CAAC;AAE5C,+BAAe,KAAK;AAAA,kBAClB,YAAY;AAAA,oBACV,WAAW,KAAK;AAAA,oBAChB,SAAS;AAAA,kBACX;AAAA,gBACF,CAAC;AAAA,cACH;AAEA;AAAA,YACF;AAAA,YACA,SAAS;AACP,oBAAM,mBAA0B;AAChC,oBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,YACzD;AAAA,UACF;AAEA,cAAI,cAAc,gBAAgB,GAAG;AACnC,2BAAe,KAAK,mBAAmB;AAAA,UACzC;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,eAAe,CAAC;AAEvD;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAEhB,cAAM,iBAAqD,CAAC;AAE5D,iBAAS,IAAI,GAAG,IAAI,MAAM,SAAS,QAAQ,KAAK;AAC9C,gBAAM,UAAU,MAAM,SAAS,CAAC;AAChC,gBAAM,gBAAgB,MAAM,MAAM,SAAS,SAAS;AACpD,gBAAM,EAAE,QAAQ,IAAI;AAEpB,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,kBAAM,OAAO,QAAQ,CAAC;AACtB,kBAAM,oBAAoB,MAAM,QAAQ,SAAS;AAEjD,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,+BAAe,KAAK;AAAA,kBAClB;AAAA;AAAA;AAAA;AAAA,oBAIE;AAAA,sBACE;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA,KAAK;AAAA,oBACP;AAAA;AAAA,gBACJ,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,aAAa;AAChB,+BAAe,KAAK;AAAA,kBAClB,kBAAkB;AAAA,oBAChB,eAAe;AAAA;AAAA;AAAA;AAAA,sBAIb,MAAM;AAAA,wBACJ;AAAA,wBACA;AAAA,wBACA;AAAA,wBACA,KAAK;AAAA,sBACP;AAAA,sBACA,WAAW,KAAK;AAAA,oBAClB;AAAA,kBACF;AAAA,gBACF,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,sBAAsB;AACzB,+BAAe,KAAK;AAAA,kBAClB,kBAAkB;AAAA,oBAChB,mBAAmB;AAAA,sBACjB,MAAM,KAAK;AAAA,oBACb;AAAA,kBACF;AAAA,gBACF,CAAC;AACD;AAAA,cACF;AAAA,cAEA,KAAK,aAAa;AAChB,+BAAe,KAAK;AAAA,kBAClB,SAAS;AAAA,oBACP,WAAW,KAAK;AAAA,oBAChB,MAAM,KAAK;AAAA,oBACX,OAAO,KAAK;AAAA,kBACd;AAAA,gBACF,CAAC;AACD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,cAAc,QAAQ,gBAAgB,GAAG;AAC3C,2BAAe,KAAK,mBAAmB;AAAA,UACzC;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,aAAa,SAAS,eAAe,CAAC;AAE5D;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO,EAAE,QAAQ,SAAS;AAC5B;AAEA,SAAS,qBAAqB,QAA8C;AAC1E,SAAO,CAAC,QAAQ,OAAO,KAAK,EAAE,SAAS,MAAM;AAC/C;AAEA,SAAS,WACP,aACA,eACA,mBACA,MACA;AACA,SAAO,eAAe,iBAAiB,oBAAoB,KAAK,KAAK,IAAI;AAC3E;AAeA,SAAS,gBACP,QACiD;AACjD,QAAM,SAA0D,CAAC;AACjE,MAAI,eACF;AAEF,aAAW,WAAW,QAAQ;AAC5B,UAAM,EAAE,KAAK,IAAI;AACjB,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,aAAI,6CAAc,UAAS,UAAU;AACnC,yBAAe,EAAE,MAAM,UAAU,UAAU,CAAC,EAAE;AAC9C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,aAAI,6CAAc,UAAS,aAAa;AACtC,yBAAe,EAAE,MAAM,aAAa,UAAU,CAAC,EAAE;AACjD,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,aAAI,6CAAc,UAAS,QAAQ;AACjC,yBAAe,EAAE,MAAM,QAAQ,UAAU,CAAC,EAAE;AAC5C,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,qBAAa,SAAS,KAAK,OAAO;AAClC;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACzWO,SAAS,uBACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;ANuBO,IAAM,2BAAN,MAA0D;AAAA,EAM/D,YACW,SACQ,UACA,QACjB;AAHS;AACQ;AACA;AARnB,SAAS,uBAAuB;AAChC,SAAS,WAAW;AACpB,SAAS,8BAA8B;AACvC,SAAS,oBAAoB;AAAA,EAM1B;AAAA,EAEK,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAGE;AAvEJ;AAwEI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,oBAAoB,MAAM;AAC5B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,mBAAmB,MAAM;AAC3B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,kBAAkB,QAAQ,eAAe,SAAS,QAAQ;AAC5D,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,EAAE,QAAQ,SAAS,IAAI,6BAA6B,MAAM;AAGhE,UAAM,yBACJ,oCAAoC;AAAA,OAClC,0DAAkB,YAAlB,mBAA2B;AAAA,IAC7B;AAEF,QAAI,CAAC,uBAAuB,SAAS;AACnC,YAAM,IAAI,sCAAqB;AAAA,QAC7B,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO,uBAAuB;AAAA,MAChC,CAAC;AAAA,IACH;AAEA,UAAM,eAAa,4BAAuB,SAAvB,mBAA6B,UAAS;AACzD,UAAM,kBACJ,kCAAuB,SAAvB,mBAA6B,iBAA7B,aACA,4BAAuB,SAAvB,mBAA6B;AAE/B,UAAM,kBAAkB;AAAA,MACtB,GAAI,aAAa,QAAQ,EAAE,UAAU;AAAA,MACrC,GAAI,eAAe,QAAQ,EAAE,YAAY;AAAA,MACzC,GAAI,QAAQ,QAAQ,EAAE,KAAK;AAAA,MAC3B,GAAI,iBAAiB,QAAQ,EAAE,cAAc;AAAA,IAC/C;AAGA,QAAI,cAAc,kBAAkB,MAAM;AACxC,UAAI,gBAAgB,aAAa,MAAM;AACrC,wBAAgB,aAAa;AAAA,MAC/B,OAAO;AACL,wBAAgB,YAAY,iBAAiB;AAAA,MAC/C;AAGA,WAAK,SAAS,+BAA+B;AAAA,QAC3C,GAAG,KAAK,SAAS;AAAA,QACjB,kBAAkB;AAAA,UAChB,OAAM,4BAAuB,SAAvB,mBAA6B;AAAA,UACnC,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAGA,QAAI,cAAc,gBAAgB,eAAe,MAAM;AACrD,aAAO,gBAAgB;AACvB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAGA,QAAI,cAAc,gBAAgB,QAAQ,MAAM;AAC9C,aAAO,gBAAgB;AACvB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,WAAiC;AAAA,MACrC;AAAA,MACA,8BAA8B,KAAK,SAAS;AAAA,MAC5C,GAAI,OAAO,KAAK,eAAe,EAAE,SAAS,KAAK;AAAA,QAC7C;AAAA,MACF;AAAA,MACA;AAAA,MACA,GAAG,qDAAkB;AAAA,IACvB;AAEA,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,cAAM,EAAE,YAAY,aAAa,IAAI,aAAa,IAAI;AACtD,eAAO;AAAA,UACL,SAAS;AAAA,YACP,GAAG;AAAA,YACH,KAAI,gBAAW,UAAX,mBAAkB,UAAS,EAAE,WAAW,IAAI,CAAC;AAAA,UACnD;AAAA,UACA,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;AAAA,QACzC;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,IAAI,+CAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,SAAS;AAAA,YACP,GAAG;AAAA,YACH,YAAY;AAAA,cACV,OAAO;AAAA,gBACL;AAAA,kBACE,UAAU;AAAA,oBACR,MAAM,KAAK,KAAK;AAAA,oBAChB,aAAa,KAAK,KAAK;AAAA,oBACvB,aAAa;AAAA,sBACX,MAAM,KAAK,KAAK;AAAA,oBAClB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA,YAAY,EAAE,MAAM,EAAE,MAAM,KAAK,KAAK,KAAK,EAAE;AAAA,YAC/C;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AA9OjE;AA+OI,UAAM,EAAE,SAAS,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAExD,UAAM,MAAM,GAAG,KAAK,OAAO,KAAK,OAAO,CAAC;AACxC,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,UAAM,sCAAc;AAAA,MAC/D;AAAA,MACA,aAAS;AAAA,QACP,UAAM,gCAAQ,KAAK,OAAO,OAAO;AAAA,QACjC,QAAQ;AAAA,MACV;AAAA,MACA,MAAM;AAAA,MACN,2BAAuB,uDAA+B;AAAA,QACpD,aAAa;AAAA,QACb,gBAAgB,WAAM;AA3P9B,cAAAC;AA2PiC,qBAAGA,MAAA,MAAM,YAAN,OAAAA,MAAiB,eAAe;AAAA;AAAA,MAC9D,CAAC;AAAA,MACD,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,UAAM,mBACJ,SAAS,SAAS,SAAS,QACvB;AAAA,MACE,SAAS;AAAA,QACP,GAAI,SAAS,SAAS,OAAO,SAAS,UAAU,WAC5C,EAAE,OAAO,SAAS,MAAoB,IACtC,CAAC;AAAA,QACL,GAAI,SAAS,SAAS;AAAA,UACpB,OAAO;AAAA,YACL,uBACE,oBAAS,UAAT,mBAAgB,yBAAhB,YAAwC,OAAO;AAAA,YACjD,wBACE,oBAAS,UAAT,mBAAgB,0BAAhB,YAAyC,OAAO;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAAA,IACF,IACA;AAEN,UAAM,YAAY,SAAS,OAAO,QAAQ,QACvC,OAAO,aAAW,QAAQ,gBAAgB,EAC1C,IAAI,aAAW;AA3RtB,UAAAA;AA4RQ,UACE,QAAQ,oBACR,mBAAmB,QAAQ,kBAC3B;AACA,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM,QAAQ,iBAAiB,cAAc;AAAA,UAC7C,GAAI,QAAQ,iBAAiB,cAAc,aAAa;AAAA,YACtD,WAAW,QAAQ,iBAAiB,cAAc;AAAA,UACpD;AAAA,QACF;AAAA,MACF,WACE,QAAQ,oBACR,uBAAuB,QAAQ,kBAC/B;AACA,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAMA,MAAA,QAAQ,iBAAiB,kBAAkB,SAA3C,OAAAA,MAAmD;AAAA,QAC3D;AAAA,MACF,OAAO;AAEL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,EAEA,OAAO,CAAC,SAA2C,SAAS,MAAS;AAExE,WAAO;AAAA,MACL,OACE,gCAAS,WAAT,mBAAiB,YAAjB,mBAA0B,YAA1B,mBACI,IAAI,UAAK;AA1TrB,YAAAA;AA0TwB,gBAAAA,MAAA,KAAK,SAAL,OAAAA,MAAa;AAAA,SAC1B,KAAK,QAFR,YAEe;AAAA,MACjB,YAAW,gCAAS,WAAT,mBAAiB,YAAjB,mBAA0B,YAA1B,mBACP,OAAO,UAAQ,CAAC,CAAC,KAAK,aADf,mBAEP,IAAI,UAAK;AA9TnB,YAAAA,KAAAC,KAAAC,KAAAC,KAAAC,KAAAC;AA8TuB;AAAA,UACb,cAAc;AAAA,UACd,aAAYJ,OAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAc,cAAd,OAAAC,MAA2B,KAAK,OAAO,WAAW;AAAA,UAC9D,WAAUE,OAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAc,SAAd,OAAAC,MAAsB,QAAQ,KAAK,OAAO,WAAW,CAAC;AAAA,UAChE,MAAM,KAAK,WAAUE,OAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAc,UAAd,OAAAC,MAAuB,EAAE;AAAA,QAChD;AAAA;AAAA,MACF,cAAc;AAAA,QACZ,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,eAAc,oBAAS,UAAT,mBAAgB,gBAAhB,YAA+B,OAAO;AAAA,QACpD,mBAAkB,oBAAS,UAAT,mBAAgB,iBAAhB,YAAgC,OAAO;AAAA,MAC3D;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,MACA,WAAW,UAAU,SAAS,IAAI,YAAY;AAAA,MAC9C,GAAI,oBAAoB,EAAE,iBAAiB;AAAA,IAC7C;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,SAAS,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AACxD,UAAM,MAAM,GAAG,KAAK,OAAO,KAAK,OAAO,CAAC;AAExC,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,UAAM,sCAAc;AAAA,MAC/D;AAAA,MACA,aAAS;AAAA,QACP,UAAM,gCAAQ,KAAK,OAAO,OAAO;AAAA,QACjC,QAAQ;AAAA,MACV;AAAA,MACA,MAAM;AAAA,MACN,2BAAuB,uDAA+B;AAAA,QACpD,aAAa;AAAA,QACb,gBAAgB,WAAS,GAAG,MAAM,IAAI,KAAK,MAAM,OAAO;AAAA,MAC1D,CAAC;AAAA,MACD,2BACE,wCAAwC,mBAAmB;AAAA,MAC7D,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,QAAI,QAAQ;AAAA,MACV,cAAc,OAAO;AAAA,MACrB,kBAAkB,OAAO;AAAA,IAC3B;AACA,QAAI,mBACF;AAEF,UAAM,wBAOF,CAAC;AAEL,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AAnYvC;AAoYY,qBAAS,aAAa,cAAmC;AACvD,6BAAe;AACf,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,aAAa,CAAC;AAAA,YAC3D;AAGA,gBAAI,CAAC,MAAM,SAAS;AAClB,2BAAa,MAAM,KAAK;AACxB;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAGpB,gBAAI,MAAM,yBAAyB;AACjC,2BAAa,MAAM,uBAAuB;AAC1C;AAAA,YACF;AACA,gBAAI,MAAM,2BAA2B;AACnC,2BAAa,MAAM,yBAAyB;AAC5C;AAAA,YACF;AACA,gBAAI,MAAM,qBAAqB;AAC7B,2BAAa,MAAM,mBAAmB;AACtC;AAAA,YACF;AACA,gBAAI,MAAM,qBAAqB;AAC7B,2BAAa,MAAM,mBAAmB;AACtC;AAAA,YACF;AAEA,gBAAI,MAAM,aAAa;AACrB,6BAAe;AAAA,gBACb,MAAM,YAAY;AAAA,cACpB;AAAA,YACF;AAEA,gBAAI,MAAM,UAAU;AAClB,sBAAQ;AAAA,gBACN,eAAc,iBAAM,SAAS,UAAf,mBAAsB,gBAAtB,YAAqC,OAAO;AAAA,gBAC1D,mBACE,iBAAM,SAAS,UAAf,mBAAsB,iBAAtB,YAAsC,OAAO;AAAA,cACjD;AAEA,oBAAM,eACJ,WAAM,SAAS,UAAf,mBAAsB,yBAAwB,UAC9C,WAAM,SAAS,UAAf,mBAAsB,0BAAyB,OAC3C;AAAA,gBACE,OAAO;AAAA,kBACL,uBACE,iBAAM,SAAS,UAAf,mBAAsB,yBAAtB,YACA,OAAO;AAAA,kBACT,wBACE,iBAAM,SAAS,UAAf,mBAAsB,0BAAtB,YACA,OAAO;AAAA,gBACX;AAAA,cACF,IACA;AAEN,oBAAM,QAAQ,MAAM,SAAS,QACzB;AAAA,gBACE,OAAO,MAAM,SAAS;AAAA,cACxB,IACA;AAEJ,kBAAI,cAAc,OAAO;AACvB,mCAAmB;AAAA,kBACjB,SAAS;AAAA,oBACP,GAAG;AAAA,oBACH,GAAG;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,kBACE,WAAM,sBAAN,mBAAyB,UACzB,UAAU,MAAM,kBAAkB,SAClC,MAAM,kBAAkB,MAAM,MAC9B;AACA,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,WAAW,MAAM,kBAAkB,MAAM;AAAA,cAC3C,CAAC;AAAA,YACH;AAEA,kBACE,WAAM,sBAAN,mBAAyB,UACzB,sBAAsB,MAAM,kBAAkB,SAC9C,MAAM,kBAAkB,MAAM,kBAC9B;AACA,oBAAM,mBACJ,MAAM,kBAAkB,MAAM;AAChC,kBAAI,UAAU,oBAAoB,iBAAiB,MAAM;AACvD,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,WAAW,iBAAiB;AAAA,gBAC9B,CAAC;AAAA,cACH,WACE,eAAe,oBACf,iBAAiB,WACjB;AACA,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,WAAW,iBAAiB;AAAA,gBAC9B,CAAC;AAAA,cACH,WAAW,UAAU,oBAAoB,iBAAiB,MAAM;AAC9D,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,MAAM,iBAAiB;AAAA,gBACzB,CAAC;AAAA,cACH;AAAA,YACF;AAEA,kBAAM,oBAAoB,MAAM;AAChC,kBAAI,4DAAmB,UAAnB,mBAA0B,YAAW,MAAM;AAC7C,oBAAM,UAAU,kBAAkB,MAAM;AACxC,oCAAsB,kBAAkB,iBAAkB,IAAI;AAAA,gBAC5D,YAAY,QAAQ;AAAA,gBACpB,UAAU,QAAQ;AAAA,gBAClB,UAAU;AAAA,cACZ;AAAA,YACF;AAEA,kBAAM,oBAAoB,MAAM;AAChC,iBACE,uDAAmB,UACnB,aAAa,kBAAkB,SAC/B,kBAAkB,MAAM,SACxB;AACA,oBAAM,eACJ,sBAAsB,kBAAkB,iBAAkB;AAC5D,oBAAM,SAAQ,uBAAkB,MAAM,QAAQ,UAAhC,YAAyC;AAEvD,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,cAAc;AAAA,gBACd,YAAY,aAAa;AAAA,gBACzB,UAAU,aAAa;AAAA,gBACvB,eAAe;AAAA,cACjB,CAAC;AAED,2BAAa,YAAY;AAAA,YAC3B;AAEA,kBAAM,mBAAmB,MAAM;AAC/B,gBAAI,oBAAoB,MAAM;AAC5B,oBAAM,QAAQ,iBAAiB;AAC/B,oBAAM,eAAe,sBAAsB,KAAK;AAGhD,kBAAI,gBAAgB,MAAM;AACxB,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,cAAc;AAAA,kBACd,YAAY,aAAa;AAAA,kBACzB,UAAU,aAAa;AAAA,kBACvB,MAAM,aAAa;AAAA,gBACrB,CAAC;AAED,uBAAO,sBAAsB,KAAK;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,UACA,MAAM,YAAY;AAChB,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA,GAAI,oBAAoB,EAAE,iBAAiB;AAAA,YAC7C,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,OAAO,SAAiB;AAC9B,UAAM,iBAAiB,mBAAmB,OAAO;AACjD,WAAO,GAAG,KAAK,OAAO,QAAQ,CAAC,UAAU,cAAc;AAAA,EACzD;AACF;AAEA,IAAM,sCAAsC,cACzC,OAAO;AAAA,EACN,MAAM,cAAE,MAAM,CAAC,cAAE,QAAQ,SAAS,GAAG,cAAE,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ;AAAA,EACrE,eAAe,cAAE,OAAO,EAAE,QAAQ;AAAA,EAClC,cAAc,cAAE,OAAO,EAAE,QAAQ;AACnC,CAAC,EACA,QAAQ;AAEX,IAAM,0BAA0B,cAAE,MAAM;AAAA,EACtC,cAAE,KAAK,oBAAoB;AAAA,EAC3B,cAAE,OAAO;AACX,CAAC;AAED,IAAM,uBAAuB,cAAE,OAAO;AAAA,EACpC,WAAW,cAAE,OAAO;AAAA,EACpB,MAAM,cAAE,OAAO;AAAA,EACf,OAAO,cAAE,QAAQ;AACnB,CAAC;AAED,IAAM,6BAA6B,cAAE,OAAO;AAAA,EAC1C,WAAW,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC9B,MAAM,cAAE,OAAO;AACjB,CAAC;AAED,IAAM,iCAAiC,cAAE,OAAO;AAAA,EAC9C,MAAM,cAAE,OAAO;AACjB,CAAC;AAID,IAAM,wBAAwB,cAAE,OAAO;AAAA,EACrC,SAAS,cACN,OAAO;AAAA,IACN,WAAW,cAAE,OAAO;AAAA,EACtB,CAAC,EACA,QAAQ;AAAA,EACX,QAAQ,cAAE,OAAO;AAAA,IACf,SAAS,cAAE,OAAO;AAAA,MAChB,SAAS,cAAE;AAAA,QACT,cAAE,OAAO;AAAA,UACP,MAAM,cAAE,OAAO,EAAE,QAAQ;AAAA,UACzB,SAAS,qBAAqB,QAAQ;AAAA,UACtC,kBAAkB,cACf,MAAM;AAAA,YACL,cAAE,OAAO;AAAA,cACP,eAAe;AAAA,YACjB,CAAC;AAAA,YACD,cAAE,OAAO;AAAA,cACP,mBAAmB;AAAA,YACrB,CAAC;AAAA,UACH,CAAC,EACA,QAAQ;AAAA,QACb,CAAC;AAAA,MACH;AAAA,MACA,MAAM,cAAE,OAAO;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AAAA,EACD,YAAY;AAAA,EACZ,OAAO,cAAE,QAAQ,EAAE,QAAQ;AAAA,EAC3B,OAAO,cAAE,OAAO;AAAA,IACd,aAAa,cAAE,OAAO;AAAA,IACtB,cAAc,cAAE,OAAO;AAAA,IACvB,aAAa,cAAE,OAAO;AAAA,IACtB,sBAAsB,cAAE,OAAO,EAAE,QAAQ;AAAA,IACzC,uBAAuB,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC5C,CAAC;AACH,CAAC;AAID,IAAM,sBAAsB,cAAE,OAAO;AAAA,EACnC,mBAAmB,cAChB,OAAO;AAAA,IACN,mBAAmB,cAAE,OAAO;AAAA,IAC5B,OAAO,cACJ,MAAM;AAAA,MACL,cAAE,OAAO,EAAE,MAAM,cAAE,OAAO,EAAE,CAAC;AAAA,MAC7B,cAAE,OAAO,EAAE,SAAS,cAAE,OAAO,EAAE,OAAO,cAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAAA,MACrD,cAAE,OAAO;AAAA,QACP,kBAAkB,cAAE,OAAO,EAAE,MAAM,cAAE,OAAO,EAAE,CAAC;AAAA,MACjD,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,kBAAkB,cAAE,OAAO;AAAA,UACzB,WAAW,cAAE,OAAO;AAAA,QACtB,CAAC;AAAA,MACH,CAAC;AAAA,MACD,cAAE,OAAO;AAAA,QACP,kBAAkB,cAAE,OAAO,EAAE,MAAM,cAAE,OAAO,EAAE,CAAC;AAAA,MACjD,CAAC;AAAA,IACH,CAAC,EACA,QAAQ;AAAA,EACb,CAAC,EACA,QAAQ;AAAA,EACX,mBAAmB,cAChB,OAAO;AAAA,IACN,mBAAmB,cAAE,OAAO;AAAA,IAC5B,OAAO,cACJ,OAAO;AAAA,MACN,SAAS,qBAAqB,QAAQ;AAAA,IACxC,CAAC,EACA,QAAQ;AAAA,EACb,CAAC,EACA,QAAQ;AAAA,EACX,kBAAkB,cACf,OAAO;AAAA,IACN,mBAAmB,cAAE,OAAO;AAAA,EAC9B,CAAC,EACA,QAAQ;AAAA,EACX,yBAAyB,cAAE,OAAO,cAAE,QAAQ,CAAC,EAAE,QAAQ;AAAA,EACvD,aAAa,cACV,OAAO;AAAA,IACN,+BAA+B,cAAE,OAAO,cAAE,QAAQ,CAAC,EAAE,QAAQ;AAAA,IAC7D,YAAY;AAAA,EACd,CAAC,EACA,QAAQ;AAAA,EACX,UAAU,cACP,OAAO;AAAA,IACN,OAAO,cAAE,QAAQ,EAAE,QAAQ;AAAA,IAC3B,OAAO,cACJ,OAAO;AAAA,MACN,sBAAsB,cAAE,OAAO,EAAE,QAAQ;AAAA,MACzC,uBAAuB,cAAE,OAAO,EAAE,QAAQ;AAAA,MAC1C,aAAa,cAAE,OAAO;AAAA,MACtB,cAAc,cAAE,OAAO;AAAA,IACzB,CAAC,EACA,QAAQ;AAAA,EACb,CAAC,EACA,QAAQ;AAAA,EACX,2BAA2B,cAAE,OAAO,cAAE,QAAQ,CAAC,EAAE,QAAQ;AAAA,EACzD,qBAAqB,cAAE,OAAO,cAAE,QAAQ,CAAC,EAAE,QAAQ;AAAA,EACnD,qBAAqB,cAAE,OAAO,cAAE,QAAQ,CAAC,EAAE,QAAQ;AACrD,CAAC;;;AOhsBD,IAAAC,yBAQO;AAMP,IAAAC,cAAkB;AAUX,IAAM,wBAAN,MAAgE;AAAA,EAMrE,YACW,SACQ,UACA,QACjB;AAHS;AACQ;AACA;AARnB,SAAS,uBAAuB;AAChC,SAAS,WAAW;AACpB,SAAS,uBAAuB;AAChC,SAAS,wBAAwB;AAAA,EAM9B;AAAA,EAEK,OAAO,SAAyB;AACtC,UAAM,iBAAiB,mBAAmB,OAAO;AACjD,WAAO,GAAG,KAAK,OAAO,QAAQ,CAAC,UAAU,cAAc;AAAA,EACzD;AAAA,EAEA,MAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEgC;AAC9B,UAAM,kBAAkB,OAAO,cAAsB;AAEnD,YAAM,OAAO;AAAA,QACX;AAAA,QACA,YAAY,KAAK,SAAS;AAAA,QAC1B,WAAW,KAAK,SAAS;AAAA,MAC3B;AACA,YAAM,MAAM,KAAK,OAAO,KAAK,OAAO;AACpC,YAAM,EAAE,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,QAC9C;AAAA,QACA,SAAS,UAAM;AAAA,cACb,uCAAe,UAAM,gCAAQ,KAAK,OAAO,OAAO,GAAG,OAAO;AAAA,QAC5D;AAAA,QACA,MAAM;AAAA,QACN,2BAAuB,uDAA+B;AAAA,UACpD,aAAa;AAAA,UACb,gBAAgB,WAAS,GAAG,MAAM,IAAI,KAAK,MAAM,OAAO;AAAA,QAC1D,CAAC;AAAA,QACD,+BAA2B;AAAA,UACzB;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AAAA,QACnB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,QACL,WAAW,SAAS;AAAA,QACpB,qBAAqB,SAAS;AAAA,MAChC;AAAA,IACF;AAEA,UAAM,YAAY,MAAM,QAAQ,IAAI,OAAO,IAAI,eAAe,CAAC;AAC/D,WAAO,UAAU;AAAA,MAIf,CAAC,aAAa,aAAa;AACzB,oBAAY,WAAW,KAAK,SAAS,SAAS;AAC9C,oBAAY,MAAM,UAAU,SAAS;AACrC,eAAO;AAAA,MACT;AAAA,MACA,EAAE,YAAY,CAAC,GAAG,OAAO,EAAE,QAAQ,EAAE,EAAE;AAAA,IACzC;AAAA,EACF;AACF;AAEA,IAAM,iCAAiC,cAAE,OAAO;AAAA,EAC9C,WAAW,cAAE,MAAM,cAAE,OAAO,CAAC;AAAA,EAC7B,qBAAqB,cAAE,OAAO;AAChC,CAAC;;;ACjGD,IAAAC,yBAQO;;;ACNA,IAAM,wBAA6D;AAAA,EACxE,2BAA2B;AAC7B;;;ADWA,IAAAC,cAAkB;AAWX,IAAM,oBAAN,MAAgD;AAAA,EAerD,YACW,SACQ,UACA,QACjB;AAHS;AACQ;AACA;AAjBnB,SAAS,uBAAuB;AAChC,SAAS,WAAW;AAAA,EAiBjB;AAAA,EAfH,IAAI,mBAA2B;AA/BjC;AAgCI,YACE,gBAAK,SAAS,qBAAd,YAAkC,sBAAsB,KAAK,OAAO,MAApE,YAAyE;AAAA,EAE7E;AAAA,EAEQ,OAAO,SAAyB;AACtC,UAAM,iBAAiB,mBAAmB,OAAO;AACjD,WAAO,GAAG,KAAK,OAAO,QAAQ,CAAC,UAAU,cAAc;AAAA,EACzD;AAAA,EAQA,MAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEE;AA3DJ;AA4DI,UAAM,WAA2C,CAAC;AAClD,UAAM,CAAC,OAAO,MAAM,IAAI,OAAO,KAAK,MAAM,GAAG,EAAE,IAAI,MAAM,IAAI,CAAC;AAC9D,UAAM,OAAO;AAAA,MACX,UAAU;AAAA,MACV,mBAAmB;AAAA,QACjB,MAAM;AAAA,QACN,KAAI,wDAAiB,YAAjB,mBAA0B,gBAC1B;AAAA,UACE,cAAc,gBAAgB,QAAQ;AAAA,QACxC,IACA,CAAC;AAAA,MACP;AAAA,MACA,uBAAuB;AAAA,QACrB,GAAI,QAAQ,EAAE,MAAM,IAAI,CAAC;AAAA,QACzB,GAAI,SAAS,EAAE,OAAO,IAAI,CAAC;AAAA,QAC3B,GAAI,OAAO,EAAE,KAAK,IAAI,CAAC;AAAA,QACvB,GAAI,IAAI,EAAE,gBAAgB,EAAE,IAAI,CAAC;AAAA,QACjC,KAAI,wDAAiB,YAAjB,mBAA0B,WAC1B,EAAE,SAAS,gBAAgB,QAAQ,QAAQ,IAC3C,CAAC;AAAA,QACL,KAAI,wDAAiB,YAAjB,mBAA0B,YAC1B,EAAE,UAAU,gBAAgB,QAAQ,SAAS,IAC7C,CAAC;AAAA,MACP;AAAA,IACF;AAEA,QAAI,eAAe,QAAW;AAC5B,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SACE;AAAA,MACJ,CAAC;AAAA,IACH;AAEA,UAAM,eAAc,sBAAK,OAAO,cAAZ,mBAAuB,gBAAvB,4CAA0C,oBAAI,KAAK;AACvE,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,UAAM,sCAAc;AAAA,MAC/D,KAAK,KAAK,OAAO,KAAK,OAAO;AAAA,MAC7B,SAAS,UAAM;AAAA,YACb,uCAAe,UAAM,gCAAQ,KAAK,OAAO,OAAO,GAAG,OAAO;AAAA,MAC5D;AAAA,MACA,MAAM;AAAA,MACN,2BAAuB,uDAA+B;AAAA,QACpD,aAAa;AAAA,QACb,gBAAgB,WAAS,GAAG,MAAM,IAAI,KAAK,MAAM,OAAO;AAAA,MAC1D,CAAC;AAAA,MACD,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA;AAAA,MACA,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,MACjB;AAAA,MACA,UAAU;AAAA,QACR,WAAW;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,6BAA6B,cAAE,OAAO;AAAA,EAC1C,QAAQ,cAAE,MAAM,cAAE,OAAO,CAAC;AAC5B,CAAC;;;AE3HM,SAAS,eACd,SACoC;AACpC,MAAI,kBAAsD,CAAC;AAC3D,MAAI,SAAS;AACX,QAAI,mBAAmB,SAAS;AAC9B,wBAAkB,uBAAuB,OAAO;AAAA,IAClD,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,iBAAW,CAAC,GAAG,CAAC,KAAK,SAAS;AAC5B,wBAAgB,EAAE,YAAY,CAAC,IAAI;AAAA,MACrC;AAAA,IACF,OAAO;AACL,wBAAkB,OAAO;AAAA,QACvB,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQO,SAAS,uBACd,SACwB;AACxB,QAAM,SAAiC,CAAC;AACxC,UAAQ,QAAQ,CAAC,OAAO,QAAQ;AAC9B,WAAO,IAAI,YAAY,CAAC,IAAI;AAAA,EAC9B,CAAC;AACD,SAAO;AACT;;;ACvCA,IAAAC,yBAIO;AACP,uBAA4B;AAgBrB,SAAS,yBACd,gBACA,QAAuB,WAAW,OACnB;AACf,SAAO,OACL,OACA,SACsB;AA7B1B;AA8BI,UAAI,kCAAM,WAAN,mBAAc,mBAAkB,UAAU,EAAC,6BAAM,OAAM;AACzD,aAAO,MAAM,OAAO,IAAI;AAAA,IAC1B;AAEA,UAAM,MACJ,OAAO,UAAU,WACb,QACA,iBAAiB,MACf,MAAM,OACN,MAAM;AAEd,UAAM,kBAAkB,eAAe,KAAK,OAAO;AACnD,UAAM,OAAO,kBAAkB,KAAK,IAAI;AACxC,UAAM,cAAc,MAAM,eAAe;AACzC,UAAM,SAAS,IAAI,6BAAY;AAAA,MAC7B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS,OAAO,YAAQ,+CAAuB,eAAe,CAAC;AAAA,MAC/D;AAAA,MACA,QAAQ,YAAY;AAAA,MACpB,aAAa,YAAY;AAAA,MACzB,iBAAiB,YAAY;AAAA,MAC7B,cAAc,YAAY;AAAA,MAC1B,SAAS;AAAA,IACX,CAAC;AAED,UAAM,gBAAgB,MAAM,OAAO,KAAK;AACxC,UAAM,gBAAgB,uBAAuB,cAAc,OAAO;AAClE,WAAO,MAAM,OAAO;AAAA,MAClB,GAAG;AAAA,MACH;AAAA,MACA,aAAS;AAAA,YACP,uCAAe,iBAAiB,aAAa;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,kBAAkB,MAAoC;AAC7D,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT,WAAW,gBAAgB,YAAY;AACrC,WAAO,IAAI,YAAY,EAAE,OAAO,IAAI;AAAA,EACtC,WAAW,gBAAgB,aAAa;AACtC,WAAO,IAAI,YAAY,EAAE,OAAO,IAAI,WAAW,IAAI,CAAC;AAAA,EACtD,OAAO;AACL,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B;AACF;;;AZsCO,SAAS,oBACd,UAAyC,CAAC,GACnB;AACvB,QAAM,aAAa,yBAAyB,YAAY;AACtD,UAAM,aAAS,oCAAY;AAAA,MACzB,cAAc,QAAQ;AAAA,MACtB,aAAa;AAAA,MACb,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC;AAED,QAAI,QAAQ,oBAAoB;AAC9B,aAAO;AAAA,QACL,GAAI,MAAM,QAAQ,mBAAmB;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA,iBAAa,oCAAY;AAAA,QACvB,cAAc,QAAQ;AAAA,QACtB,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,MACD,qBAAiB,oCAAY;AAAA,QAC3B,cAAc,QAAQ;AAAA,QACtB,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,MACD,kBAAc,4CAAoB;AAAA,QAChC,cAAc,QAAQ;AAAA,QACtB,yBAAyB;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF,GAAG,QAAQ,KAAK;AAEhB,QAAM,aAAa,MAAW;AA1JhC;AA2JI;AAAA,OACE,aAAQ,YAAR,YACE,+BAA2B,oCAAY;AAAA,QACrC,cAAc,QAAQ;AAAA,QACtB,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC,CAAC;AAAA,IACN,MARA,YAQK;AAAA;AAEP,QAAM,kBAAkB,CACtB,SACA,WAAgC,CAAC,MACjC;AAxKJ;AAyKI,eAAI,yBAAyB,SAAS,UAAU;AAAA,MAC9C,SAAS;AAAA,MACT,UAAS,aAAQ,YAAR,YAAmB,CAAC;AAAA,MAC7B,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,QAAM,uBAAuB,CAC3B,SACA,WAAqC,CAAC,MACtC;AAhMJ;AAiMI,eAAI,sBAAsB,SAAS,UAAU;AAAA,MAC3C,SAAS;AAAA,MACT,UAAS,aAAQ,YAAR,YAAmB,CAAC;AAAA,MAC7B,OAAO;AAAA,IACT,CAAC;AAAA;AAEH,QAAM,mBAAmB,CACvB,SACA,WAAiC,CAAC,MAClC;AA1MJ;AA2MI,eAAI,kBAAkB,SAAS,UAAU;AAAA,MACvC,SAAS;AAAA,MACT,UAAS,aAAQ,YAAR,YAAmB,CAAC;AAAA,MAC7B,OAAO;AAAA,IACT,CAAC;AAAA;AAEH,WAAS,gBAAgB;AACzB,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAC9B,WAAS,QAAQ;AACjB,WAAS,aAAa;AAEtB,SAAO;AACT;AAKO,IAAM,UAAU,oBAAoB;", "names": ["import_provider_utils", "import_provider", "import_provider_utils", "import_zod", "import_provider", "import_provider", "import_provider_utils", "i", "part", "_a", "_b", "_c", "_d", "_e", "_f", "import_provider_utils", "import_zod", "import_provider_utils", "import_zod", "import_provider_utils"]}