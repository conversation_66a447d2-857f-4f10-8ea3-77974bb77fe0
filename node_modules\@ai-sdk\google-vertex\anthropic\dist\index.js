"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/anthropic/index.ts
var anthropic_exports = {};
__export(anthropic_exports, {
  createVertexAnthropic: () => createVertexAnthropic2,
  vertexAnthropic: () => vertexAnthropic
});
module.exports = __toCommonJS(anthropic_exports);

// src/anthropic/google-vertex-anthropic-provider-node.ts
var import_provider_utils2 = require("@ai-sdk/provider-utils");

// src/google-vertex-auth-google-auth-library.ts
var import_google_auth_library = require("google-auth-library");
var authInstance = null;
var authOptions = null;
function getAuth(options) {
  if (!authInstance || options !== authOptions) {
    authInstance = new import_google_auth_library.GoogleAuth({
      scopes: ["https://www.googleapis.com/auth/cloud-platform"],
      ...options
    });
    authOptions = options;
  }
  return authInstance;
}
async function generateAuthToken(options) {
  const auth = getAuth(options || {});
  const client = await auth.getClient();
  const token = await client.getAccessToken();
  return (token == null ? void 0 : token.token) || null;
}

// src/anthropic/google-vertex-anthropic-provider.ts
var import_provider = require("@ai-sdk/provider");
var import_provider_utils = require("@ai-sdk/provider-utils");
var import_internal = require("@ai-sdk/anthropic/internal");
function createVertexAnthropic(options = {}) {
  var _a;
  const location = (0, import_provider_utils.loadOptionalSetting)({
    settingValue: options.location,
    environmentVariableName: "GOOGLE_VERTEX_LOCATION"
  });
  const project = (0, import_provider_utils.loadOptionalSetting)({
    settingValue: options.project,
    environmentVariableName: "GOOGLE_VERTEX_PROJECT"
  });
  const baseURL = (_a = (0, import_provider_utils.withoutTrailingSlash)(options.baseURL)) != null ? _a : `https://${location}-aiplatform.googleapis.com/v1/projects/${project}/locations/${location}/publishers/anthropic/models`;
  const createChatModel = (modelId, settings = {}) => {
    var _a2;
    return new import_internal.AnthropicMessagesLanguageModel(
      modelId,
      settings,
      {
        provider: "vertex.anthropic.messages",
        baseURL,
        headers: (_a2 = options.headers) != null ? _a2 : {},
        fetch: options.fetch,
        supportsImageUrls: false,
        buildRequestUrl: (baseURL2, isStreaming) => `${baseURL2}/${modelId}:${isStreaming ? "streamRawPredict" : "rawPredict"}`,
        transformRequestBody: (args) => {
          const { model, ...rest } = args;
          return {
            ...rest,
            anthropic_version: "vertex-2023-10-16"
          };
        }
      }
    );
  };
  const provider = function(modelId, settings) {
    if (new.target) {
      throw new Error(
        "The Anthropic model function cannot be called with the new keyword."
      );
    }
    return createChatModel(modelId, settings);
  };
  provider.languageModel = createChatModel;
  provider.chat = createChatModel;
  provider.messages = createChatModel;
  provider.textEmbeddingModel = (modelId) => {
    throw new import_provider.NoSuchModelError({ modelId, modelType: "textEmbeddingModel" });
  };
  provider.tools = import_internal.anthropicTools;
  return provider;
}

// src/anthropic/google-vertex-anthropic-provider-node.ts
function createVertexAnthropic2(options = {}) {
  return createVertexAnthropic({
    ...options,
    headers: async () => ({
      Authorization: `Bearer ${await generateAuthToken(
        options.googleAuthOptions
      )}`,
      ...await (0, import_provider_utils2.resolve)(options.headers)
    })
  });
}
var vertexAnthropic = createVertexAnthropic2();
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  createVertexAnthropic,
  vertexAnthropic
});
//# sourceMappingURL=index.js.map