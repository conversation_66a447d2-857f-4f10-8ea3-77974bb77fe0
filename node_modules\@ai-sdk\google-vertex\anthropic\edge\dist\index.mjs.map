{"version": 3, "sources": ["../../../src/anthropic/edge/google-vertex-anthropic-provider-edge.ts", "../../../src/edge/google-vertex-auth-edge.ts", "../../../src/anthropic/google-vertex-anthropic-provider.ts"], "sourcesContent": ["import { resolve } from '@ai-sdk/provider-utils';\nimport {\n  generateAuthToken,\n  GoogleCredentials,\n} from '../../edge/google-vertex-auth-edge';\nimport {\n  createVertexAnthropic as createVertexAnthropicOriginal,\n  GoogleVertexAnthropicProvider,\n  GoogleVertexAnthropicProviderSettings as GoogleVertexAnthropicProviderSettingsOriginal,\n} from '../google-vertex-anthropic-provider';\n\nexport type { GoogleVertexAnthropicProvider };\n\nexport interface GoogleVertexAnthropicProviderSettings\n  extends GoogleVertexAnthropicProviderSettingsOriginal {\n  /**\n   * Optional. The Google credentials for the Google Cloud service account. If\n   * not provided, the Google Vertex provider will use environment variables to\n   * load the credentials.\n   */\n  googleCredentials?: GoogleCredentials;\n}\n\nexport function createVertexAnthropic(\n  options: GoogleVertexAnthropicProviderSettings = {},\n): GoogleVertexAnthropicProvider {\n  return createVertexAnthropicOriginal({\n    ...options,\n    headers: async () => ({\n      Authorization: `Bearer ${await generateAuthToken(\n        options.googleCredentials,\n      )}`,\n      ...(await resolve(options.headers)),\n    }),\n  });\n}\n\n/**\n * Default Google Vertex AI Anthropic provider instance.\n */\nexport const vertexAnthropic = createVertexAnthropic();\n", "import { loadOptionalSetting, loadSetting } from '@ai-sdk/provider-utils';\n\nexport interface GoogleCredentials {\n  /**\n   * The client email for the Google Cloud service account. Defaults to the\n   * value of the `GOOGLE_CLIENT_EMAIL` environment variable.\n   */\n  clientEmail: string;\n\n  /**\n   * The private key for the Google Cloud service account. Defaults to the\n   * value of the `GOOGLE_PRIVATE_KEY` environment variable.\n   */\n  privateKey: string;\n\n  /**\n   * Optional. The private key ID for the Google Cloud service account. Defaults\n   * to the value of the `GOOGLE_PRIVATE_KEY_ID` environment variable.\n   */\n  privateKeyId?: string;\n}\n\nconst loadCredentials = async (): Promise<GoogleCredentials> => {\n  try {\n    return {\n      clientEmail: loadSetting({\n        settingValue: undefined,\n        settingName: 'clientEmail',\n        environmentVariableName: 'GOOGLE_CLIENT_EMAIL',\n        description: 'Google client email',\n      }),\n      privateKey: loadSetting({\n        settingValue: undefined,\n        settingName: 'privateKey',\n        environmentVariableName: 'GOOGLE_PRIVATE_KEY',\n        description: 'Google private key',\n      }),\n      privateKeyId: loadOptionalSetting({\n        settingValue: undefined,\n        environmentVariableName: 'GOOGLE_PRIVATE_KEY_ID',\n      }),\n    };\n  } catch (error: any) {\n    throw new Error(`Failed to load Google credentials: ${error.message}`);\n  }\n};\n\n// Convert a string to base64url\nconst base64url = (str: string) => {\n  return btoa(str).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n};\nconst importPrivateKey = async (pemKey: string) => {\n  const pemHeader = '-----BEGIN PRIVATE KEY-----';\n  const pemFooter = '-----END PRIVATE KEY-----';\n\n  // Remove header, footer, and any whitespace/newlines\n  const pemContents = pemKey\n    .replace(pemHeader, '')\n    .replace(pemFooter, '')\n    .replace(/\\s/g, '');\n\n  // Decode base64 to binary\n  const binaryString = atob(pemContents);\n\n  // Convert binary string to Uint8Array\n  const binaryData = new Uint8Array(binaryString.length);\n  for (let i = 0; i < binaryString.length; i++) {\n    binaryData[i] = binaryString.charCodeAt(i);\n  }\n\n  return await crypto.subtle.importKey(\n    'pkcs8',\n    binaryData,\n    { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' },\n    true,\n    ['sign'],\n  );\n};\n\nconst buildJwt = async (credentials: GoogleCredentials) => {\n  const now = Math.floor(Date.now() / 1000);\n\n  // Only include kid in header if privateKeyId is provided\n  const header: { alg: string; typ: string; kid?: string } = {\n    alg: 'RS256',\n    typ: 'JWT',\n  };\n\n  if (credentials.privateKeyId) {\n    header.kid = credentials.privateKeyId;\n  }\n\n  const payload = {\n    iss: credentials.clientEmail,\n    scope: 'https://www.googleapis.com/auth/cloud-platform',\n    aud: 'https://oauth2.googleapis.com/token',\n    exp: now + 3600,\n    iat: now,\n  };\n\n  const privateKey = await importPrivateKey(credentials.privateKey);\n\n  const signingInput = `${base64url(JSON.stringify(header))}.${base64url(\n    JSON.stringify(payload),\n  )}`;\n  const encoder = new TextEncoder();\n  const data = encoder.encode(signingInput);\n\n  const signature = await crypto.subtle.sign(\n    'RSASSA-PKCS1-v1_5',\n    privateKey,\n    data,\n  );\n\n  const signatureBase64 = base64url(\n    String.fromCharCode(...new Uint8Array(signature)),\n  );\n\n  return `${base64url(JSON.stringify(header))}.${base64url(\n    JSON.stringify(payload),\n  )}.${signatureBase64}`;\n};\n\n/**\n * Generate an authentication token for Google Vertex AI in a manner compatible\n * with the Edge runtime.\n */\nexport async function generateAuthToken(credentials?: GoogleCredentials) {\n  try {\n    const creds = credentials || (await loadCredentials());\n    const jwt = await buildJwt(creds);\n\n    const response = await fetch('https://oauth2.googleapis.com/token', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n      body: new URLSearchParams({\n        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',\n        assertion: jwt,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Token request failed: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.access_token;\n  } catch (error) {\n    throw error;\n  }\n}\n", "import {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  Resolvable,\n  loadOptionalSetting,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport {\n  anthropicTools,\n  AnthropicMessagesLanguageModel,\n  AnthropicMessagesModelId,\n} from '@ai-sdk/anthropic/internal';\nimport {\n  GoogleVertexAnthropicMessagesModelId,\n  GoogleVertexAnthropicMessagesSettings,\n} from './google-vertex-anthropic-messages-settings';\nexport interface GoogleVertexAnthropicProvider extends ProviderV1 {\n  /**\nCreates a model for text generation.\n*/\n  (\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings?: GoogleVertexAnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a model for text generation.\n*/\n  languageModel(\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings?: GoogleVertexAnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nAnthropic-specific computer use tool.\n   */\n  tools: typeof anthropicTools;\n}\n\nexport interface GoogleVertexAnthropicProviderSettings {\n  /**\n   * Google Cloud project ID. Defaults to the value of the `GOOGLE_VERTEX_PROJECT` environment variable.\n   */\n  project?: string;\n\n  /**\n   * Google Cloud region. Defaults to the value of the `GOOGLE_VERTEX_LOCATION` environment variable.\n   */\n  location?: string;\n\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://api.anthropic.com/v1`.\n   */\n  baseURL?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Resolvable<Record<string, string | undefined>>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n}\n\n/**\nCreate a Google Vertex Anthropic provider instance.\n */\nexport function createVertexAnthropic(\n  options: GoogleVertexAnthropicProviderSettings = {},\n): GoogleVertexAnthropicProvider {\n  const location = loadOptionalSetting({\n    settingValue: options.location,\n    environmentVariableName: 'GOOGLE_VERTEX_LOCATION',\n  });\n  const project = loadOptionalSetting({\n    settingValue: options.project,\n    environmentVariableName: 'GOOGLE_VERTEX_PROJECT',\n  });\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ??\n    `https://${location}-aiplatform.googleapis.com/v1/projects/${project}/locations/${location}/publishers/anthropic/models`;\n\n  const createChatModel = (\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings: GoogleVertexAnthropicMessagesSettings = {},\n  ) =>\n    new AnthropicMessagesLanguageModel(\n      modelId as AnthropicMessagesModelId,\n      settings,\n      {\n        provider: 'vertex.anthropic.messages',\n        baseURL,\n        headers: options.headers ?? {},\n        fetch: options.fetch,\n        supportsImageUrls: false,\n        buildRequestUrl: (baseURL, isStreaming) =>\n          `${baseURL}/${modelId}:${\n            isStreaming ? 'streamRawPredict' : 'rawPredict'\n          }`,\n        transformRequestBody: args => {\n          // Remove model from args and add anthropic version\n          const { model, ...rest } = args;\n          return {\n            ...rest,\n            anthropic_version: 'vertex-2023-10-16',\n          };\n        },\n      },\n    );\n\n  const provider = function (\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings?: GoogleVertexAnthropicMessagesSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Anthropic model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.messages = createChatModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n\n  provider.tools = anthropicTools;\n\n  return provider;\n}\n"], "mappings": ";AAAA,SAAS,eAAe;;;ACAxB,SAAS,qBAAqB,mBAAmB;AAsBjD,IAAM,kBAAkB,YAAwC;AAC9D,MAAI;AACF,WAAO;AAAA,MACL,aAAa,YAAY;AAAA,QACvB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY,YAAY;AAAA,QACtB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,MACD,cAAc,oBAAoB;AAAA,QAChC,cAAc;AAAA,QACd,yBAAyB;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF,SAAS,OAAY;AACnB,UAAM,IAAI,MAAM,sCAAsC,MAAM,OAAO,EAAE;AAAA,EACvE;AACF;AAGA,IAAM,YAAY,CAAC,QAAgB;AACjC,SAAO,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AAC3E;AACA,IAAM,mBAAmB,OAAO,WAAmB;AACjD,QAAM,YAAY;AAClB,QAAM,YAAY;AAGlB,QAAM,cAAc,OACjB,QAAQ,WAAW,EAAE,EACrB,QAAQ,WAAW,EAAE,EACrB,QAAQ,OAAO,EAAE;AAGpB,QAAM,eAAe,KAAK,WAAW;AAGrC,QAAM,aAAa,IAAI,WAAW,aAAa,MAAM;AACrD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,eAAW,CAAC,IAAI,aAAa,WAAW,CAAC;AAAA,EAC3C;AAEA,SAAO,MAAM,OAAO,OAAO;AAAA,IACzB;AAAA,IACA;AAAA,IACA,EAAE,MAAM,qBAAqB,MAAM,UAAU;AAAA,IAC7C;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AACF;AAEA,IAAM,WAAW,OAAO,gBAAmC;AACzD,QAAM,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAGxC,QAAM,SAAqD;AAAA,IACzD,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,MAAI,YAAY,cAAc;AAC5B,WAAO,MAAM,YAAY;AAAA,EAC3B;AAEA,QAAM,UAAU;AAAA,IACd,KAAK,YAAY;AAAA,IACjB,OAAO;AAAA,IACP,KAAK;AAAA,IACL,KAAK,MAAM;AAAA,IACX,KAAK;AAAA,EACP;AAEA,QAAM,aAAa,MAAM,iBAAiB,YAAY,UAAU;AAEhE,QAAM,eAAe,GAAG,UAAU,KAAK,UAAU,MAAM,CAAC,CAAC,IAAI;AAAA,IAC3D,KAAK,UAAU,OAAO;AAAA,EACxB,CAAC;AACD,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,OAAO,QAAQ,OAAO,YAAY;AAExC,QAAM,YAAY,MAAM,OAAO,OAAO;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,kBAAkB;AAAA,IACtB,OAAO,aAAa,GAAG,IAAI,WAAW,SAAS,CAAC;AAAA,EAClD;AAEA,SAAO,GAAG,UAAU,KAAK,UAAU,MAAM,CAAC,CAAC,IAAI;AAAA,IAC7C,KAAK,UAAU,OAAO;AAAA,EACxB,CAAC,IAAI,eAAe;AACtB;AAMA,eAAsB,kBAAkB,aAAiC;AACvE,MAAI;AACF,UAAM,QAAQ,eAAgB,MAAM,gBAAgB;AACpD,UAAM,MAAM,MAAM,SAAS,KAAK;AAEhC,UAAM,WAAW,MAAM,MAAM,uCAAuC;AAAA,MAClE,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,oCAAoC;AAAA,MAC/D,MAAM,IAAI,gBAAgB;AAAA,QACxB,YAAY;AAAA,QACZ,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAED,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,yBAAyB,SAAS,UAAU,EAAE;AAAA,IAChE;AAEA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;AAAA,EACd,SAAS,OAAO;AACd,UAAM;AAAA,EACR;AACF;;;ACtJA;AAAA,EAEE;AAAA,OAEK;AACP;AAAA,EAGE,uBAAAA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,OAEK;AA4DA,SAAS,sBACd,UAAiD,CAAC,GACnB;AA7EjC;AA8EE,QAAM,WAAWA,qBAAoB;AAAA,IACnC,cAAc,QAAQ;AAAA,IACtB,yBAAyB;AAAA,EAC3B,CAAC;AACD,QAAM,UAAUA,qBAAoB;AAAA,IAClC,cAAc,QAAQ;AAAA,IACtB,yBAAyB;AAAA,EAC3B,CAAC;AACD,QAAM,WACJ,0BAAqB,QAAQ,OAAO,MAApC,YACA,WAAW,QAAQ,0CAA0C,OAAO,cAAc,QAAQ;AAE5F,QAAM,kBAAkB,CACtB,SACA,WAAkD,CAAC,MACnD;AA7FJ,QAAAC;AA8FI,eAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,QACE,UAAU;AAAA,QACV;AAAA,QACA,UAASA,MAAA,QAAQ,YAAR,OAAAA,MAAmB,CAAC;AAAA,QAC7B,OAAO,QAAQ;AAAA,QACf,mBAAmB;AAAA,QACnB,iBAAiB,CAACC,UAAS,gBACzB,GAAGA,QAAO,IAAI,OAAO,IACnB,cAAc,qBAAqB,YACrC;AAAA,QACF,sBAAsB,UAAQ;AAE5B,gBAAM,EAAE,OAAO,GAAG,KAAK,IAAI;AAC3B,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAEF,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,WAAW;AACpB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,iBAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AAEA,WAAS,QAAQ;AAEjB,SAAO;AACT;;;AFtHO,SAASC,uBACd,UAAiD,CAAC,GACnB;AAC/B,SAAO,sBAA8B;AAAA,IACnC,GAAG;AAAA,IACH,SAAS,aAAa;AAAA,MACpB,eAAe,UAAU,MAAM;AAAA,QAC7B,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,GAAI,MAAM,QAAQ,QAAQ,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;AAKO,IAAM,kBAAkBA,uBAAsB;", "names": ["loadOptionalSetting", "_a", "baseURL", "createVertexAnthropic"]}