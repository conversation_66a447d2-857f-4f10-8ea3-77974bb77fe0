import { GoogleAuthOptions } from 'google-auth-library';
import { ProviderV1, LanguageModelV1 } from '@ai-sdk/provider';
import { Resolvable, FetchFunction } from '@ai-sdk/provider-utils';
import { AnthropicMessagesSettings, anthropicTools } from '@ai-sdk/anthropic/internal';

type GoogleVertexAnthropicMessagesModelId = 'claude-3-7-sonnet@20250219' | 'claude-3-5-sonnet-v2@20241022' | 'claude-3-5-haiku@20241022' | 'claude-3-5-sonnet@20240620' | 'claude-3-haiku@20240307' | 'claude-3-sonnet@20240229' | 'claude-3-opus@20240229' | (string & {});
interface GoogleVertexAnthropicMessagesSettings extends AnthropicMessagesSettings {
}

interface GoogleVertexAnthropicProvider extends ProviderV1 {
    /**
  Creates a model for text generation.
  */
    (modelId: GoogleVertexAnthropicMessagesModelId, settings?: GoogleVertexAnthropicMessagesSettings): LanguageModelV1;
    /**
  Creates a model for text generation.
  */
    languageModel(modelId: GoogleVertexAnthropicMessagesModelId, settings?: GoogleVertexAnthropicMessagesSettings): LanguageModelV1;
    /**
  Anthropic-specific computer use tool.
     */
    tools: typeof anthropicTools;
}
interface GoogleVertexAnthropicProviderSettings$1 {
    /**
     * Google Cloud project ID. Defaults to the value of the `GOOGLE_VERTEX_PROJECT` environment variable.
     */
    project?: string;
    /**
     * Google Cloud region. Defaults to the value of the `GOOGLE_VERTEX_LOCATION` environment variable.
     */
    location?: string;
    /**
  Use a different URL prefix for API calls, e.g. to use proxy servers.
  The default prefix is `https://api.anthropic.com/v1`.
     */
    baseURL?: string;
    /**
  Custom headers to include in the requests.
       */
    headers?: Resolvable<Record<string, string | undefined>>;
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
      */
    fetch?: FetchFunction;
}

interface GoogleVertexAnthropicProviderSettings extends GoogleVertexAnthropicProviderSettings$1 {
    /**
   Optional. The Authentication options provided by google-auth-library.
  Complete list of authentication options is documented in the
  GoogleAuthOptions interface:
  https://github.com/googleapis/google-auth-library-nodejs/blob/main/src/auth/googleauth.ts.
     */
    googleAuthOptions?: GoogleAuthOptions;
}
declare function createVertexAnthropic(options?: GoogleVertexAnthropicProviderSettings): GoogleVertexAnthropicProvider;
/**
Default Google Vertex Anthropic provider instance.
 */
declare const vertexAnthropic: GoogleVertexAnthropicProvider;

export { type GoogleVertexAnthropicProvider, type GoogleVertexAnthropicProviderSettings, createVertexAnthropic, vertexAnthropic };
