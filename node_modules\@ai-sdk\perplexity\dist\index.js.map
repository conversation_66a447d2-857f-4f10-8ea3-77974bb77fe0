{"version": 3, "sources": ["../src/index.ts", "../src/perplexity-provider.ts", "../src/perplexity-language-model.ts", "../src/convert-to-perplexity-messages.ts", "../src/map-perplexity-finish-reason.ts"], "sourcesContent": ["export { createPerplexity, perplexity } from './perplexity-provider';\nexport type {\n  PerplexityProvider,\n  PerplexityProviderSettings,\n} from './perplexity-provider';\n", "import {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  generateId,\n  loadApiKey,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { PerplexityLanguageModel } from './perplexity-language-model';\nimport { PerplexityLanguageModelId } from './perplexity-language-model-settings';\n\nexport interface PerplexityProvider extends ProviderV1 {\n  /**\nCreates an Perplexity chat model for text generation.\n   */\n  (modelId: PerplexityLanguageModelId): LanguageModelV1;\n\n  /**\nCreates an Perplexity language model for text generation.\n   */\n  languageModel(modelId: PerplexityLanguageModelId): LanguageModelV1;\n}\n\nexport interface PerplexityProviderSettings {\n  /**\nBase URL for the perplexity API calls.\n     */\n  baseURL?: string;\n\n  /**\nAPI key for authenticating requests.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n   */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n  */\n  fetch?: FetchFunction;\n}\n\nexport function createPerplexity(\n  options: PerplexityProviderSettings = {},\n): PerplexityProvider {\n  const getHeaders = () => ({\n    Authorization: `Bearer ${loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'PERPLEXITY_API_KEY',\n      description: 'Perplexity',\n    })}`,\n    ...options.headers,\n  });\n\n  const createLanguageModel = (modelId: PerplexityLanguageModelId) => {\n    return new PerplexityLanguageModel(modelId, {\n      baseURL: withoutTrailingSlash(\n        options.baseURL ?? 'https://api.perplexity.ai',\n      )!,\n      headers: getHeaders,\n      generateId,\n      fetch: options.fetch,\n    });\n  };\n\n  const provider = (modelId: PerplexityLanguageModelId) =>\n    createLanguageModel(modelId);\n\n  provider.languageModel = createLanguageModel;\n\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n\n  return provider;\n}\n\nexport const perplexity = createPerplexity();\n", "import {\n  LanguageModelV1,\n  LanguageModelV1Call<PERSON>arning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1StreamPart,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonErrorResponseHandler,\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { PerplexityLanguageModelId } from './perplexity-language-model-settings';\nimport { convertToPerplexityMessages } from './convert-to-perplexity-messages';\nimport { mapPerplexityFinishReason } from './map-perplexity-finish-reason';\n\ntype PerplexityChatConfig = {\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  generateId: () => string;\n  fetch?: FetchFunction;\n};\n\nexport class PerplexityLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'json';\n  readonly supportsStructuredOutputs = true;\n  readonly supportsImageUrls = false;\n  readonly provider = 'perplexity';\n\n  readonly modelId: PerplexityLanguageModelId;\n\n  private readonly config: PerplexityChatConfig;\n\n  constructor(\n    modelId: PerplexityLanguageModelId,\n    config: PerplexityChatConfig,\n  ) {\n    this.modelId = modelId;\n    this.config = config;\n  }\n\n  private getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    if (topK != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'topK',\n      });\n    }\n\n    if (stopSequences != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'stopSequences',\n      });\n    }\n\n    if (seed != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'seed',\n      });\n    }\n\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n\n      // standardized settings:\n      frequency_penalty: frequencyPenalty,\n      max_tokens: maxTokens,\n      presence_penalty: presencePenalty,\n      temperature,\n      top_k: topK,\n      top_p: topP,\n\n      // response format:\n      response_format:\n        responseFormat?.type === 'json'\n          ? {\n              type: 'json_schema',\n              json_schema: { schema: responseFormat.schema },\n            }\n          : undefined,\n\n      // provider extensions\n      ...(providerMetadata?.perplexity ?? {}),\n\n      // messages:\n      messages: convertToPerplexityMessages(prompt),\n    };\n\n    switch (type) {\n      case 'regular': {\n        return { args: baseArgs, warnings };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: {\n              type: 'json_schema',\n              json_schema: { schema: mode.schema },\n            },\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'tool-mode object generation',\n        });\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = this.getArgs(options);\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: `${this.config.baseURL}/chat/completions`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: createJsonErrorResponseHandler({\n        errorSchema: perplexityErrorSchema,\n        errorToMessage,\n      }),\n      successfulResponseHandler: createJsonResponseHandler(\n        perplexityResponseSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n    const text = choice.message.content;\n\n    return {\n      text,\n      toolCalls: [],\n      finishReason: mapPerplexityFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: response.usage?.prompt_tokens ?? Number.NaN,\n        completionTokens: response.usage?.completion_tokens ?? Number.NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      request: { body: JSON.stringify(args) },\n      response: getResponseMetadata(response),\n      warnings,\n      sources: response.citations?.map(url => ({\n        sourceType: 'url',\n        id: this.config.generateId(),\n        url,\n      })),\n      providerMetadata: {\n        perplexity: {\n          images:\n            response.images?.map(image => ({\n              imageUrl: image.image_url,\n              originUrl: image.origin_url,\n              height: image.height,\n              width: image.width,\n            })) ?? null,\n          usage: {\n            citationTokens: response.usage?.citation_tokens ?? null,\n            numSearchQueries: response.usage?.num_search_queries ?? null,\n          },\n        },\n      },\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = this.getArgs(options);\n\n    const body = { ...args, stream: true };\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/chat/completions`,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: createJsonErrorResponseHandler({\n        errorSchema: perplexityErrorSchema,\n        errorToMessage,\n      }),\n      successfulResponseHandler: createEventSourceResponseHandler(\n        perplexityChunkSchema,\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { messages: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    const providerMetadata: {\n      perplexity: {\n        usage: {\n          citationTokens: number | null;\n          numSearchQueries: number | null;\n        };\n        images: Array<{\n          imageUrl: string;\n          originUrl: string;\n          height: number;\n          width: number;\n        }> | null;\n      };\n    } = {\n      perplexity: {\n        usage: {\n          citationTokens: null,\n          numSearchQueries: null,\n        },\n        images: null,\n      },\n    };\n    let isFirstChunk = true;\n\n    const self = this;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof perplexityChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            if (isFirstChunk) {\n              controller.enqueue({\n                type: 'response-metadata',\n                ...getResponseMetadata(value),\n              });\n\n              value.citations?.forEach(url => {\n                controller.enqueue({\n                  type: 'source',\n                  source: {\n                    sourceType: 'url',\n                    id: self.config.generateId(),\n                    url,\n                  },\n                });\n              });\n\n              isFirstChunk = false;\n            }\n\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens,\n              };\n\n              providerMetadata.perplexity.usage = {\n                citationTokens: value.usage.citation_tokens ?? null,\n                numSearchQueries: value.usage.num_search_queries ?? null,\n              };\n            }\n\n            if (value.images != null) {\n              providerMetadata.perplexity.images = value.images.map(image => ({\n                imageUrl: image.image_url,\n                originUrl: image.origin_url,\n                height: image.height,\n                width: image.width,\n              }));\n            }\n\n            const choice = value.choices[0];\n            if (choice?.finish_reason != null) {\n              finishReason = mapPerplexityFinishReason(choice.finish_reason);\n            }\n\n            if (choice?.delta == null) {\n              return;\n            }\n\n            const delta = choice.delta;\n            const textContent = delta.content;\n\n            if (textContent != null) {\n              controller.enqueue({\n                type: 'text-delta',\n                textDelta: textContent,\n              });\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage,\n              providerMetadata,\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings,\n    };\n  }\n}\n\nfunction getResponseMetadata({\n  id,\n  model,\n  created,\n}: {\n  id: string;\n  created: number;\n  model: string;\n}) {\n  return {\n    id,\n    modelId: model,\n    timestamp: new Date(created * 1000),\n  };\n}\n\nconst perplexityUsageSchema = z.object({\n  prompt_tokens: z.number(),\n  completion_tokens: z.number(),\n  citation_tokens: z.number().nullish(),\n  num_search_queries: z.number().nullish(),\n});\n\nexport const perplexityImageSchema = z.object({\n  image_url: z.string(),\n  origin_url: z.string(),\n  height: z.number(),\n  width: z.number(),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst perplexityResponseSchema = z.object({\n  id: z.string(),\n  created: z.number(),\n  model: z.string(),\n  choices: z.array(\n    z.object({\n      message: z.object({\n        role: z.literal('assistant'),\n        content: z.string(),\n      }),\n      finish_reason: z.string().nullish(),\n    }),\n  ),\n  citations: z.array(z.string()).nullish(),\n  images: z.array(perplexityImageSchema).nullish(),\n  usage: perplexityUsageSchema.nullish(),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst perplexityChunkSchema = z.object({\n  id: z.string(),\n  created: z.number(),\n  model: z.string(),\n  choices: z.array(\n    z.object({\n      delta: z.object({\n        role: z.literal('assistant'),\n        content: z.string(),\n      }),\n      finish_reason: z.string().nullish(),\n    }),\n  ),\n  citations: z.array(z.string()).nullish(),\n  images: z.array(perplexityImageSchema).nullish(),\n  usage: perplexityUsageSchema.nullish(),\n});\n\nexport const perplexityErrorSchema = z.object({\n  error: z.object({\n    code: z.number(),\n    message: z.string().nullish(),\n    type: z.string().nullish(),\n  }),\n});\n\nexport type PerplexityErrorData = z.infer<typeof perplexityErrorSchema>;\n\nconst errorToMessage = (data: PerplexityErrorData) => {\n  return data.error.message ?? data.error.type ?? 'unknown error';\n};\n", "import {\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { PerplexityPrompt } from './perplexity-language-model-prompt';\n\nexport function convertToPerplexityMessages(\n  prompt: LanguageModelV1Prompt,\n): PerplexityPrompt {\n  const messages: PerplexityPrompt = [];\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        messages.push({ role: 'system', content });\n        break;\n      }\n\n      case 'user':\n      case 'assistant': {\n        messages.push({\n          role,\n          content: content\n            .filter(\n              part =>\n                part.type !== 'reasoning' && part.type !== 'redacted-reasoning',\n            )\n            .map(part => {\n              switch (part.type) {\n                case 'text': {\n                  return part.text;\n                }\n                case 'image': {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'Image content parts in user messages',\n                  });\n                }\n                case 'file': {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'File content parts in user messages',\n                  });\n                }\n                case 'tool-call': {\n                  throw new UnsupportedFunctionalityError({\n                    functionality: 'Tool calls in assistant messages',\n                  });\n                }\n                default: {\n                  const _exhaustiveCheck: never = part;\n                  throw new Error(`Unsupported part: ${_exhaustiveCheck}`);\n                }\n              }\n            })\n            .join(''),\n        });\n        break;\n      }\n      case 'tool': {\n        throw new UnsupportedFunctionalityError({\n          functionality: 'Tool messages',\n        });\n      }\n      default: {\n        const _exhaustiveCheck: never = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  return messages;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapPerplexityFinishReason(\n  finishReason: string | null | undefined,\n): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop':\n    case 'length':\n      return finishReason;\n    default:\n      return 'unknown';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,mBAIO;AACP,IAAAC,yBAKO;;;ACVP,IAAAC,mBAMO;AACP,4BAQO;AACP,iBAAkB;;;AChBlB,sBAGO;AAGA,SAAS,4BACd,QACkB;AAClB,QAAM,WAA6B,CAAC;AAEpC,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,iBAAS,KAAK,EAAE,MAAM,UAAU,QAAQ,CAAC;AACzC;AAAA,MACF;AAAA,MAEA,KAAK;AAAA,MACL,KAAK,aAAa;AAChB,iBAAS,KAAK;AAAA,UACZ;AAAA,UACA,SAAS,QACN;AAAA,YACC,UACE,KAAK,SAAS,eAAe,KAAK,SAAS;AAAA,UAC/C,EACC,IAAI,UAAQ;AACX,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO,KAAK;AAAA,cACd;AAAA,cACA,KAAK,SAAS;AACZ,sBAAM,IAAI,8CAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,cACA,KAAK,QAAQ;AACX,sBAAM,IAAI,8CAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,cACA,KAAK,aAAa;AAChB,sBAAM,IAAI,8CAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,cACA,SAAS;AACP,sBAAM,mBAA0B;AAChC,sBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,cACzD;AAAA,YACF;AAAA,UACF,CAAC,EACA,KAAK,EAAE;AAAA,QACZ,CAAC;AACD;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,cAAM,IAAI,8CAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MACA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACpEO,SAAS,0BACd,cAC6B;AAC7B,UAAQ,cAAc;AAAA,IACpB,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;AFgBO,IAAM,0BAAN,MAAyD;AAAA,EAW9D,YACE,SACA,QACA;AAbF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AACvC,SAAS,4BAA4B;AACrC,SAAS,oBAAoB;AAC7B,SAAS,WAAW;AAUlB,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EAEQ,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAiD;AA5DnD;AA6DI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,iBAAiB,MAAM;AACzB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,UAAM,WAAW;AAAA;AAAA,MAEf,OAAO,KAAK;AAAA;AAAA,MAGZ,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA;AAAA,MAGP,kBACE,iDAAgB,UAAS,SACrB;AAAA,QACE,MAAM;AAAA,QACN,aAAa,EAAE,QAAQ,eAAe,OAAO;AAAA,MAC/C,IACA;AAAA;AAAA,MAGN,IAAI,0DAAkB,eAAlB,YAAgC,CAAC;AAAA;AAAA,MAGrC,UAAU,4BAA4B,MAAM;AAAA,IAC9C;AAEA,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,eAAO,EAAE,MAAM,UAAU,SAAS;AAAA,MACpC;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,iBAAiB;AAAA,cACf,MAAM;AAAA,cACN,aAAa,EAAE,QAAQ,KAAK,OAAO;AAAA,YACrC;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,cAAM,IAAI,+CAA8B;AAAA,UACtC,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AAnJjE;AAoJI,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAE/C,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,IAAI,UAAM,qCAAc;AAAA,MACtB,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,MAC3B,aAAS,sCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,MAAM;AAAA,MACN,2BAAuB,sDAA+B;AAAA,QACpD,aAAa;AAAA,QACb;AAAA,MACF,CAAC;AAAA,MACD,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAChD,UAAM,SAAS,SAAS,QAAQ,CAAC;AACjC,UAAM,OAAO,OAAO,QAAQ;AAE5B,WAAO;AAAA,MACL;AAAA,MACA,WAAW,CAAC;AAAA,MACZ,cAAc,0BAA0B,OAAO,aAAa;AAAA,MAC5D,OAAO;AAAA,QACL,eAAc,oBAAS,UAAT,mBAAgB,kBAAhB,YAAiC,OAAO;AAAA,QACtD,mBAAkB,oBAAS,UAAT,mBAAgB,sBAAhB,YAAqC,OAAO;AAAA,MAChE;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,iBAAiB,MAAM,YAAY;AAAA,MAC3D,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;AAAA,MACtC,UAAU,oBAAoB,QAAQ;AAAA,MACtC;AAAA,MACA,UAAS,cAAS,cAAT,mBAAoB,IAAI,UAAQ;AAAA,QACvC,YAAY;AAAA,QACZ,IAAI,KAAK,OAAO,WAAW;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB,YAAY;AAAA,UACV,SACE,oBAAS,WAAT,mBAAiB,IAAI,YAAU;AAAA,YAC7B,UAAU,MAAM;AAAA,YAChB,WAAW,MAAM;AAAA,YACjB,QAAQ,MAAM;AAAA,YACd,OAAO,MAAM;AAAA,UACf,QALA,YAKO;AAAA,UACT,OAAO;AAAA,YACL,iBAAgB,oBAAS,UAAT,mBAAgB,oBAAhB,YAAmC;AAAA,YACnD,mBAAkB,oBAAS,UAAT,mBAAgB,uBAAhB,YAAsC;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO;AAE/C,UAAM,OAAO,EAAE,GAAG,MAAM,QAAQ,KAAK;AAErC,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,qCAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,MAC3B,aAAS,sCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D;AAAA,MACA,2BAAuB,sDAA+B;AAAA,QACpD,aAAa;AAAA,QACb;AAAA,MACF,CAAC;AAAA,MACD,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,QAAI,QAA4D;AAAA,MAC9D,cAAc,OAAO;AAAA,MACrB,kBAAkB,OAAO;AAAA,IAC3B;AACA,UAAM,mBAaF;AAAA,MACF,YAAY;AAAA,QACV,OAAO;AAAA,UACL,gBAAgB;AAAA,UAChB,kBAAkB;AAAA,QACpB;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,eAAe;AAEnB,UAAM,OAAO;AAEb,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AA9QvC;AA+QY,gBAAI,CAAC,MAAM,SAAS;AAClB,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,gBAAI,cAAc;AAChB,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,GAAG,oBAAoB,KAAK;AAAA,cAC9B,CAAC;AAED,0BAAM,cAAN,mBAAiB,QAAQ,SAAO;AAC9B,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,QAAQ;AAAA,oBACN,YAAY;AAAA,oBACZ,IAAI,KAAK,OAAO,WAAW;AAAA,oBAC3B;AAAA,kBACF;AAAA,gBACF,CAAC;AAAA,cACH;AAEA,6BAAe;AAAA,YACjB;AAEA,gBAAI,MAAM,SAAS,MAAM;AACvB,sBAAQ;AAAA,gBACN,cAAc,MAAM,MAAM;AAAA,gBAC1B,kBAAkB,MAAM,MAAM;AAAA,cAChC;AAEA,+BAAiB,WAAW,QAAQ;AAAA,gBAClC,iBAAgB,WAAM,MAAM,oBAAZ,YAA+B;AAAA,gBAC/C,mBAAkB,WAAM,MAAM,uBAAZ,YAAkC;AAAA,cACtD;AAAA,YACF;AAEA,gBAAI,MAAM,UAAU,MAAM;AACxB,+BAAiB,WAAW,SAAS,MAAM,OAAO,IAAI,YAAU;AAAA,gBAC9D,UAAU,MAAM;AAAA,gBAChB,WAAW,MAAM;AAAA,gBACjB,QAAQ,MAAM;AAAA,gBACd,OAAO,MAAM;AAAA,cACf,EAAE;AAAA,YACJ;AAEA,kBAAM,SAAS,MAAM,QAAQ,CAAC;AAC9B,iBAAI,iCAAQ,kBAAiB,MAAM;AACjC,6BAAe,0BAA0B,OAAO,aAAa;AAAA,YAC/D;AAEA,iBAAI,iCAAQ,UAAS,MAAM;AACzB;AAAA,YACF;AAEA,kBAAM,QAAQ,OAAO;AACrB,kBAAM,cAAc,MAAM;AAE1B,gBAAI,eAAe,MAAM;AACvB,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,WAAW;AAAA,cACb,CAAC;AAAA,YACH;AAAA,UACF;AAAA,UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,SAAO;AAAA,IACL;AAAA,IACA,SAAS;AAAA,IACT,WAAW,IAAI,KAAK,UAAU,GAAI;AAAA,EACpC;AACF;AAEA,IAAM,wBAAwB,aAAE,OAAO;AAAA,EACrC,eAAe,aAAE,OAAO;AAAA,EACxB,mBAAmB,aAAE,OAAO;AAAA,EAC5B,iBAAiB,aAAE,OAAO,EAAE,QAAQ;AAAA,EACpC,oBAAoB,aAAE,OAAO,EAAE,QAAQ;AACzC,CAAC;AAEM,IAAM,wBAAwB,aAAE,OAAO;AAAA,EAC5C,WAAW,aAAE,OAAO;AAAA,EACpB,YAAY,aAAE,OAAO;AAAA,EACrB,QAAQ,aAAE,OAAO;AAAA,EACjB,OAAO,aAAE,OAAO;AAClB,CAAC;AAID,IAAM,2BAA2B,aAAE,OAAO;AAAA,EACxC,IAAI,aAAE,OAAO;AAAA,EACb,SAAS,aAAE,OAAO;AAAA,EAClB,OAAO,aAAE,OAAO;AAAA,EAChB,SAAS,aAAE;AAAA,IACT,aAAE,OAAO;AAAA,MACP,SAAS,aAAE,OAAO;AAAA,QAChB,MAAM,aAAE,QAAQ,WAAW;AAAA,QAC3B,SAAS,aAAE,OAAO;AAAA,MACpB,CAAC;AAAA,MACD,eAAe,aAAE,OAAO,EAAE,QAAQ;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,WAAW,aAAE,MAAM,aAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,EACvC,QAAQ,aAAE,MAAM,qBAAqB,EAAE,QAAQ;AAAA,EAC/C,OAAO,sBAAsB,QAAQ;AACvC,CAAC;AAID,IAAM,wBAAwB,aAAE,OAAO;AAAA,EACrC,IAAI,aAAE,OAAO;AAAA,EACb,SAAS,aAAE,OAAO;AAAA,EAClB,OAAO,aAAE,OAAO;AAAA,EAChB,SAAS,aAAE;AAAA,IACT,aAAE,OAAO;AAAA,MACP,OAAO,aAAE,OAAO;AAAA,QACd,MAAM,aAAE,QAAQ,WAAW;AAAA,QAC3B,SAAS,aAAE,OAAO;AAAA,MACpB,CAAC;AAAA,MACD,eAAe,aAAE,OAAO,EAAE,QAAQ;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,WAAW,aAAE,MAAM,aAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,EACvC,QAAQ,aAAE,MAAM,qBAAqB,EAAE,QAAQ;AAAA,EAC/C,OAAO,sBAAsB,QAAQ;AACvC,CAAC;AAEM,IAAM,wBAAwB,aAAE,OAAO;AAAA,EAC5C,OAAO,aAAE,OAAO;AAAA,IACd,MAAM,aAAE,OAAO;AAAA,IACf,SAAS,aAAE,OAAO,EAAE,QAAQ;AAAA,IAC5B,MAAM,aAAE,OAAO,EAAE,QAAQ;AAAA,EAC3B,CAAC;AACH,CAAC;AAID,IAAM,iBAAiB,CAAC,SAA8B;AArbtD;AAsbE,UAAO,gBAAK,MAAM,YAAX,YAAsB,KAAK,MAAM,SAAjC,YAAyC;AAClD;;;ADtYO,SAAS,iBACd,UAAsC,CAAC,GACnB;AACpB,QAAM,aAAa,OAAO;AAAA,IACxB,eAAe,cAAU,mCAAW;AAAA,MAClC,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC,CAAC;AAAA,IACF,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,sBAAsB,CAAC,YAAuC;AA7DtE;AA8DI,WAAO,IAAI,wBAAwB,SAAS;AAAA,MAC1C,aAAS;AAAA,SACP,aAAQ,YAAR,YAAmB;AAAA,MACrB;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,OAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,QAAM,WAAW,CAAC,YAChB,oBAAoB,OAAO;AAE7B,WAAS,gBAAgB;AAEzB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,kCAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AAEA,SAAO;AACT;AAEO,IAAM,aAAa,iBAAiB;", "names": ["import_provider", "import_provider_utils", "import_provider"]}