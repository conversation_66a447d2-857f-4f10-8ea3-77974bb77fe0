// src/bedrock-provider.ts
import {
  generateId,
  loadOptionalSetting,
  loadSetting,
  withoutTrailingSlash
} from "@ai-sdk/provider-utils";

// src/bedrock-chat-language-model.ts
import {
  InvalidArgumentError,
  UnsupportedFunctionalityError as UnsupportedFunctionalityError3
} from "@ai-sdk/provider";
import {
  combineHeaders,
  createJsonErrorResponseHandler,
  createJsonResponseHandler,
  postJsonToApi,
  resolve
} from "@ai-sdk/provider-utils";
import { z as z2 } from "zod";

// src/bedrock-api-types.ts
var BEDROCK_CACHE_POINT = {
  cachePoint: { type: "default" }
};
var BEDROCK_STOP_REASONS = [
  "stop",
  "stop_sequence",
  "end_turn",
  "length",
  "max_tokens",
  "content-filter",
  "content_filtered",
  "guardrail_intervened",
  "tool-calls",
  "tool_use"
];

// src/bedrock-error.ts
import { z } from "zod";
var BedrockErrorSchema = z.object({
  message: z.string(),
  type: z.string().nullish()
});

// src/bedrock-event-stream-response-handler.ts
import { EmptyResponseBodyError } from "@ai-sdk/provider";
import {
  safeParseJSON,
  extractResponseHeaders,
  safeValidateTypes
} from "@ai-sdk/provider-utils";
import { EventStreamCodec } from "@smithy/eventstream-codec";
import { toUtf8, fromUtf8 } from "@smithy/util-utf8";
var createBedrockEventStreamResponseHandler = (chunkSchema) => async ({ response }) => {
  const responseHeaders = extractResponseHeaders(response);
  if (response.body == null) {
    throw new EmptyResponseBodyError({});
  }
  const codec = new EventStreamCodec(toUtf8, fromUtf8);
  let buffer = new Uint8Array(0);
  const textDecoder = new TextDecoder();
  return {
    responseHeaders,
    value: response.body.pipeThrough(
      new TransformStream({
        transform(chunk, controller) {
          var _a, _b;
          const newBuffer = new Uint8Array(buffer.length + chunk.length);
          newBuffer.set(buffer);
          newBuffer.set(chunk, buffer.length);
          buffer = newBuffer;
          while (buffer.length >= 4) {
            const totalLength = new DataView(
              buffer.buffer,
              buffer.byteOffset,
              buffer.byteLength
            ).getUint32(0, false);
            if (buffer.length < totalLength) {
              break;
            }
            try {
              const subView = buffer.subarray(0, totalLength);
              const decoded = codec.decode(subView);
              buffer = buffer.slice(totalLength);
              if (((_a = decoded.headers[":message-type"]) == null ? void 0 : _a.value) === "event") {
                const data = textDecoder.decode(decoded.body);
                const parsedDataResult = safeParseJSON({ text: data });
                if (!parsedDataResult.success) {
                  controller.enqueue(parsedDataResult);
                  break;
                }
                delete parsedDataResult.value.p;
                let wrappedData = {
                  [(_b = decoded.headers[":event-type"]) == null ? void 0 : _b.value]: parsedDataResult.value
                };
                const validatedWrappedData = safeValidateTypes({
                  value: wrappedData,
                  schema: chunkSchema
                });
                if (!validatedWrappedData.success) {
                  controller.enqueue(validatedWrappedData);
                } else {
                  controller.enqueue({
                    success: true,
                    value: validatedWrappedData.value,
                    rawValue: wrappedData
                  });
                }
              }
            } catch (e) {
              break;
            }
          }
        }
      })
    )
  };
};

// src/bedrock-prepare-tools.ts
import {
  UnsupportedFunctionalityError
} from "@ai-sdk/provider";
function prepareTools(mode) {
  var _a;
  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;
  if (tools == null) {
    return {
      toolConfig: { tools: void 0, toolChoice: void 0 },
      toolWarnings: []
    };
  }
  const toolWarnings = [];
  const bedrockTools = [];
  for (const tool of tools) {
    if (tool.type === "provider-defined") {
      toolWarnings.push({ type: "unsupported-tool", tool });
    } else {
      bedrockTools.push({
        toolSpec: {
          name: tool.name,
          description: tool.description,
          inputSchema: {
            json: tool.parameters
          }
        }
      });
    }
  }
  const toolChoice = mode.toolChoice;
  if (toolChoice == null) {
    return {
      toolConfig: { tools: bedrockTools, toolChoice: void 0 },
      toolWarnings
    };
  }
  const type = toolChoice.type;
  switch (type) {
    case "auto":
      return {
        toolConfig: { tools: bedrockTools, toolChoice: { auto: {} } },
        toolWarnings
      };
    case "required":
      return {
        toolConfig: { tools: bedrockTools, toolChoice: { any: {} } },
        toolWarnings
      };
    case "none":
      return {
        toolConfig: { tools: void 0, toolChoice: void 0 },
        toolWarnings
      };
    case "tool":
      return {
        toolConfig: {
          tools: bedrockTools,
          toolChoice: { tool: { name: toolChoice.toolName } }
        },
        toolWarnings
      };
    default: {
      const _exhaustiveCheck = type;
      throw new UnsupportedFunctionalityError({
        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`
      });
    }
  }
}

// src/convert-to-bedrock-chat-messages.ts
import {
  UnsupportedFunctionalityError as UnsupportedFunctionalityError2
} from "@ai-sdk/provider";
import {
  convertUint8ArrayToBase64,
  createIdGenerator
} from "@ai-sdk/provider-utils";
var generateFileId = createIdGenerator({ prefix: "file", size: 16 });
function getCachePoint(providerMetadata) {
  var _a;
  return (_a = providerMetadata == null ? void 0 : providerMetadata.bedrock) == null ? void 0 : _a.cachePoint;
}
function convertToBedrockChatMessages(prompt) {
  var _a, _b, _c, _d, _e;
  const blocks = groupIntoBlocks(prompt);
  let system = [];
  const messages = [];
  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i];
    const isLastBlock = i === blocks.length - 1;
    const type = block.type;
    switch (type) {
      case "system": {
        if (messages.length > 0) {
          throw new UnsupportedFunctionalityError2({
            functionality: "Multiple system messages that are separated by user/assistant messages"
          });
        }
        for (const message of block.messages) {
          system.push({ text: message.content });
          if (getCachePoint(message.providerMetadata)) {
            system.push(BEDROCK_CACHE_POINT);
          }
        }
        break;
      }
      case "user": {
        const bedrockContent = [];
        for (const message of block.messages) {
          const { role, content, providerMetadata } = message;
          switch (role) {
            case "user": {
              for (let j = 0; j < content.length; j++) {
                const part = content[j];
                switch (part.type) {
                  case "text": {
                    bedrockContent.push({
                      text: part.text
                    });
                    break;
                  }
                  case "image": {
                    if (part.image instanceof URL) {
                      throw new UnsupportedFunctionalityError2({
                        functionality: "Image URLs in user messages"
                      });
                    }
                    bedrockContent.push({
                      image: {
                        format: (_b = (_a = part.mimeType) == null ? void 0 : _a.split(
                          "/"
                        )) == null ? void 0 : _b[1],
                        source: {
                          bytes: convertUint8ArrayToBase64(
                            (_c = part.image) != null ? _c : part.image
                          )
                        }
                      }
                    });
                    break;
                  }
                  case "file": {
                    if (part.data instanceof URL) {
                      throw new UnsupportedFunctionalityError2({
                        functionality: "File URLs in user messages"
                      });
                    }
                    bedrockContent.push({
                      document: {
                        format: (_e = (_d = part.mimeType) == null ? void 0 : _d.split(
                          "/"
                        )) == null ? void 0 : _e[1],
                        name: generateFileId(),
                        source: {
                          bytes: part.data
                        }
                      }
                    });
                    break;
                  }
                }
              }
              break;
            }
            case "tool": {
              for (let i2 = 0; i2 < content.length; i2++) {
                const part = content[i2];
                const toolResultContent = part.content != void 0 ? part.content.map((part2) => {
                  switch (part2.type) {
                    case "text":
                      return {
                        text: part2.text
                      };
                    case "image":
                      if (!part2.mimeType) {
                        throw new Error(
                          "Image mime type is required in tool result part content"
                        );
                      }
                      const format = part2.mimeType.split("/")[1];
                      if (!isBedrockImageFormat(format)) {
                        throw new Error(
                          `Unsupported image format: ${format}`
                        );
                      }
                      return {
                        image: {
                          format,
                          source: {
                            bytes: part2.data
                          }
                        }
                      };
                  }
                }) : [{ text: JSON.stringify(part.result) }];
                bedrockContent.push({
                  toolResult: {
                    toolUseId: part.toolCallId,
                    content: toolResultContent
                  }
                });
              }
              break;
            }
            default: {
              const _exhaustiveCheck = role;
              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);
            }
          }
          if (getCachePoint(providerMetadata)) {
            bedrockContent.push(BEDROCK_CACHE_POINT);
          }
        }
        messages.push({ role: "user", content: bedrockContent });
        break;
      }
      case "assistant": {
        const bedrockContent = [];
        for (let j = 0; j < block.messages.length; j++) {
          const message = block.messages[j];
          const isLastMessage = j === block.messages.length - 1;
          const { content } = message;
          for (let k = 0; k < content.length; k++) {
            const part = content[k];
            const isLastContentPart = k === content.length - 1;
            switch (part.type) {
              case "text": {
                bedrockContent.push({
                  text: (
                    // trim the last text part if it's the last message in the block
                    // because Bedrock does not allow trailing whitespace
                    // in pre-filled assistant responses
                    trimIfLast(
                      isLastBlock,
                      isLastMessage,
                      isLastContentPart,
                      part.text
                    )
                  )
                });
                break;
              }
              case "reasoning": {
                bedrockContent.push({
                  reasoningContent: {
                    reasoningText: {
                      // trim the last text part if it's the last message in the block
                      // because Bedrock does not allow trailing whitespace
                      // in pre-filled assistant responses
                      text: trimIfLast(
                        isLastBlock,
                        isLastMessage,
                        isLastContentPart,
                        part.text
                      ),
                      signature: part.signature
                    }
                  }
                });
                break;
              }
              case "redacted-reasoning": {
                bedrockContent.push({
                  reasoningContent: {
                    redactedReasoning: {
                      data: part.data
                    }
                  }
                });
                break;
              }
              case "tool-call": {
                bedrockContent.push({
                  toolUse: {
                    toolUseId: part.toolCallId,
                    name: part.toolName,
                    input: part.args
                  }
                });
                break;
              }
            }
          }
          if (getCachePoint(message.providerMetadata)) {
            bedrockContent.push(BEDROCK_CACHE_POINT);
          }
        }
        messages.push({ role: "assistant", content: bedrockContent });
        break;
      }
      default: {
        const _exhaustiveCheck = type;
        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);
      }
    }
  }
  return { system, messages };
}
function isBedrockImageFormat(format) {
  return ["jpeg", "png", "gif"].includes(format);
}
function trimIfLast(isLastBlock, isLastMessage, isLastContentPart, text) {
  return isLastBlock && isLastMessage && isLastContentPart ? text.trim() : text;
}
function groupIntoBlocks(prompt) {
  const blocks = [];
  let currentBlock = void 0;
  for (const message of prompt) {
    const { role } = message;
    switch (role) {
      case "system": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "system") {
          currentBlock = { type: "system", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      case "assistant": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "assistant") {
          currentBlock = { type: "assistant", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      case "user": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "user") {
          currentBlock = { type: "user", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      case "tool": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "user") {
          currentBlock = { type: "user", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      default: {
        const _exhaustiveCheck = role;
        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);
      }
    }
  }
  return blocks;
}

// src/map-bedrock-finish-reason.ts
function mapBedrockFinishReason(finishReason) {
  switch (finishReason) {
    case "stop_sequence":
    case "end_turn":
      return "stop";
    case "max_tokens":
      return "length";
    case "content_filtered":
    case "guardrail_intervened":
      return "content-filter";
    case "tool_use":
      return "tool-calls";
    default:
      return "unknown";
  }
}

// src/bedrock-chat-language-model.ts
var BedrockChatLanguageModel = class {
  constructor(modelId, settings, config) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
    this.specificationVersion = "v1";
    this.provider = "amazon-bedrock";
    this.defaultObjectGenerationMode = "tool";
    this.supportsImageUrls = false;
  }
  getArgs({
    mode,
    prompt,
    maxTokens,
    temperature,
    topP,
    topK,
    frequencyPenalty,
    presencePenalty,
    stopSequences,
    responseFormat,
    seed,
    providerMetadata
  }) {
    var _a, _b, _c, _d, _e, _f, _g;
    const type = mode.type;
    const warnings = [];
    if (frequencyPenalty != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "frequencyPenalty"
      });
    }
    if (presencePenalty != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "presencePenalty"
      });
    }
    if (seed != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "seed"
      });
    }
    if (topK != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "topK"
      });
    }
    if (responseFormat != null && responseFormat.type !== "text") {
      warnings.push({
        type: "unsupported-setting",
        setting: "responseFormat",
        details: "JSON response format is not supported."
      });
    }
    const { system, messages } = convertToBedrockChatMessages(prompt);
    const reasoningConfigOptions = BedrockReasoningConfigOptionsSchema.safeParse(
      (_a = providerMetadata == null ? void 0 : providerMetadata.bedrock) == null ? void 0 : _a.reasoning_config
    );
    if (!reasoningConfigOptions.success) {
      throw new InvalidArgumentError({
        argument: "providerOptions.bedrock.reasoning_config",
        message: "invalid reasoning configuration options",
        cause: reasoningConfigOptions.error
      });
    }
    const isThinking = ((_b = reasoningConfigOptions.data) == null ? void 0 : _b.type) === "enabled";
    const thinkingBudget = (_e = (_c = reasoningConfigOptions.data) == null ? void 0 : _c.budgetTokens) != null ? _e : (_d = reasoningConfigOptions.data) == null ? void 0 : _d.budget_tokens;
    const inferenceConfig = {
      ...maxTokens != null && { maxTokens },
      ...temperature != null && { temperature },
      ...topP != null && { topP },
      ...stopSequences != null && { stopSequences }
    };
    if (isThinking && thinkingBudget != null) {
      if (inferenceConfig.maxTokens != null) {
        inferenceConfig.maxTokens += thinkingBudget;
      } else {
        inferenceConfig.maxTokens = thinkingBudget + 4096;
      }
      this.settings.additionalModelRequestFields = {
        ...this.settings.additionalModelRequestFields,
        reasoning_config: {
          type: (_f = reasoningConfigOptions.data) == null ? void 0 : _f.type,
          budget_tokens: thinkingBudget
        }
      };
    }
    if (isThinking && inferenceConfig.temperature != null) {
      delete inferenceConfig.temperature;
      warnings.push({
        type: "unsupported-setting",
        setting: "temperature",
        details: "temperature is not supported when thinking is enabled"
      });
    }
    if (isThinking && inferenceConfig.topP != null) {
      delete inferenceConfig.topP;
      warnings.push({
        type: "unsupported-setting",
        setting: "topP",
        details: "topP is not supported when thinking is enabled"
      });
    }
    const baseArgs = {
      system,
      additionalModelRequestFields: this.settings.additionalModelRequestFields,
      ...Object.keys(inferenceConfig).length > 0 && {
        inferenceConfig
      },
      messages,
      ...providerMetadata == null ? void 0 : providerMetadata.bedrock
    };
    switch (type) {
      case "regular": {
        const { toolConfig, toolWarnings } = prepareTools(mode);
        return {
          command: {
            ...baseArgs,
            ...((_g = toolConfig.tools) == null ? void 0 : _g.length) ? { toolConfig } : {}
          },
          warnings: [...warnings, ...toolWarnings]
        };
      }
      case "object-json": {
        throw new UnsupportedFunctionalityError3({
          functionality: "json-mode object generation"
        });
      }
      case "object-tool": {
        return {
          command: {
            ...baseArgs,
            toolConfig: {
              tools: [
                {
                  toolSpec: {
                    name: mode.tool.name,
                    description: mode.tool.description,
                    inputSchema: {
                      json: mode.tool.parameters
                    }
                  }
                }
              ],
              toolChoice: { tool: { name: mode.tool.name } }
            }
          },
          warnings
        };
      }
      default: {
        const _exhaustiveCheck = type;
        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);
      }
    }
  }
  async doGenerate(options) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p;
    const { command: args, warnings } = this.getArgs(options);
    const url = `${this.getUrl(this.modelId)}/converse`;
    const { value: response, responseHeaders } = await postJsonToApi({
      url,
      headers: combineHeaders(
        await resolve(this.config.headers),
        options.headers
      ),
      body: args,
      failedResponseHandler: createJsonErrorResponseHandler({
        errorSchema: BedrockErrorSchema,
        errorToMessage: (error) => {
          var _a2;
          return `${(_a2 = error.message) != null ? _a2 : "Unknown error"}`;
        }
      }),
      successfulResponseHandler: createJsonResponseHandler(
        BedrockResponseSchema
      ),
      abortSignal: options.abortSignal,
      fetch: this.config.fetch
    });
    const { messages: rawPrompt, ...rawSettings } = args;
    const providerMetadata = response.trace || response.usage ? {
      bedrock: {
        ...response.trace && typeof response.trace === "object" ? { trace: response.trace } : {},
        ...response.usage && {
          usage: {
            cacheReadInputTokens: (_b = (_a = response.usage) == null ? void 0 : _a.cacheReadInputTokens) != null ? _b : Number.NaN,
            cacheWriteInputTokens: (_d = (_c = response.usage) == null ? void 0 : _c.cacheWriteInputTokens) != null ? _d : Number.NaN
          }
        }
      }
    } : void 0;
    const reasoning = response.output.message.content.filter((content) => content.reasoningContent).map((content) => {
      var _a2;
      if (content.reasoningContent && "reasoningText" in content.reasoningContent) {
        return {
          type: "text",
          text: content.reasoningContent.reasoningText.text,
          ...content.reasoningContent.reasoningText.signature && {
            signature: content.reasoningContent.reasoningText.signature
          }
        };
      } else if (content.reasoningContent && "redactedReasoning" in content.reasoningContent) {
        return {
          type: "redacted",
          data: (_a2 = content.reasoningContent.redactedReasoning.data) != null ? _a2 : ""
        };
      } else {
        return void 0;
      }
    }).filter((item) => item !== void 0);
    return {
      text: (_h = (_g = (_f = (_e = response.output) == null ? void 0 : _e.message) == null ? void 0 : _f.content) == null ? void 0 : _g.map((part) => {
        var _a2;
        return (_a2 = part.text) != null ? _a2 : "";
      }).join("")) != null ? _h : void 0,
      toolCalls: (_l = (_k = (_j = (_i = response.output) == null ? void 0 : _i.message) == null ? void 0 : _j.content) == null ? void 0 : _k.filter((part) => !!part.toolUse)) == null ? void 0 : _l.map((part) => {
        var _a2, _b2, _c2, _d2, _e2, _f2;
        return {
          toolCallType: "function",
          toolCallId: (_b2 = (_a2 = part.toolUse) == null ? void 0 : _a2.toolUseId) != null ? _b2 : this.config.generateId(),
          toolName: (_d2 = (_c2 = part.toolUse) == null ? void 0 : _c2.name) != null ? _d2 : `tool-${this.config.generateId()}`,
          args: JSON.stringify((_f2 = (_e2 = part.toolUse) == null ? void 0 : _e2.input) != null ? _f2 : "")
        };
      }),
      finishReason: mapBedrockFinishReason(
        response.stopReason
      ),
      usage: {
        promptTokens: (_n = (_m = response.usage) == null ? void 0 : _m.inputTokens) != null ? _n : Number.NaN,
        completionTokens: (_p = (_o = response.usage) == null ? void 0 : _o.outputTokens) != null ? _p : Number.NaN
      },
      rawCall: { rawPrompt, rawSettings },
      rawResponse: { headers: responseHeaders },
      warnings,
      reasoning: reasoning.length > 0 ? reasoning : void 0,
      ...providerMetadata && { providerMetadata }
    };
  }
  async doStream(options) {
    const { command: args, warnings } = this.getArgs(options);
    const url = `${this.getUrl(this.modelId)}/converse-stream`;
    const { value: response, responseHeaders } = await postJsonToApi({
      url,
      headers: combineHeaders(
        await resolve(this.config.headers),
        options.headers
      ),
      body: args,
      failedResponseHandler: createJsonErrorResponseHandler({
        errorSchema: BedrockErrorSchema,
        errorToMessage: (error) => `${error.type}: ${error.message}`
      }),
      successfulResponseHandler: createBedrockEventStreamResponseHandler(BedrockStreamSchema),
      abortSignal: options.abortSignal,
      fetch: this.config.fetch
    });
    const { messages: rawPrompt, ...rawSettings } = args;
    let finishReason = "unknown";
    let usage = {
      promptTokens: Number.NaN,
      completionTokens: Number.NaN
    };
    let providerMetadata = void 0;
    const toolCallContentBlocks = {};
    return {
      stream: response.pipeThrough(
        new TransformStream({
          transform(chunk, controller) {
            var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;
            function enqueueError(bedrockError) {
              finishReason = "error";
              controller.enqueue({ type: "error", error: bedrockError });
            }
            if (!chunk.success) {
              enqueueError(chunk.error);
              return;
            }
            const value = chunk.value;
            if (value.internalServerException) {
              enqueueError(value.internalServerException);
              return;
            }
            if (value.modelStreamErrorException) {
              enqueueError(value.modelStreamErrorException);
              return;
            }
            if (value.throttlingException) {
              enqueueError(value.throttlingException);
              return;
            }
            if (value.validationException) {
              enqueueError(value.validationException);
              return;
            }
            if (value.messageStop) {
              finishReason = mapBedrockFinishReason(
                value.messageStop.stopReason
              );
            }
            if (value.metadata) {
              usage = {
                promptTokens: (_b = (_a = value.metadata.usage) == null ? void 0 : _a.inputTokens) != null ? _b : Number.NaN,
                completionTokens: (_d = (_c = value.metadata.usage) == null ? void 0 : _c.outputTokens) != null ? _d : Number.NaN
              };
              const cacheUsage = ((_e = value.metadata.usage) == null ? void 0 : _e.cacheReadInputTokens) != null || ((_f = value.metadata.usage) == null ? void 0 : _f.cacheWriteInputTokens) != null ? {
                usage: {
                  cacheReadInputTokens: (_h = (_g = value.metadata.usage) == null ? void 0 : _g.cacheReadInputTokens) != null ? _h : Number.NaN,
                  cacheWriteInputTokens: (_j = (_i = value.metadata.usage) == null ? void 0 : _i.cacheWriteInputTokens) != null ? _j : Number.NaN
                }
              } : void 0;
              const trace = value.metadata.trace ? {
                trace: value.metadata.trace
              } : void 0;
              if (cacheUsage || trace) {
                providerMetadata = {
                  bedrock: {
                    ...cacheUsage,
                    ...trace
                  }
                };
              }
            }
            if (((_k = value.contentBlockDelta) == null ? void 0 : _k.delta) && "text" in value.contentBlockDelta.delta && value.contentBlockDelta.delta.text) {
              controller.enqueue({
                type: "text-delta",
                textDelta: value.contentBlockDelta.delta.text
              });
            }
            if (((_l = value.contentBlockDelta) == null ? void 0 : _l.delta) && "reasoningContent" in value.contentBlockDelta.delta && value.contentBlockDelta.delta.reasoningContent) {
              const reasoningContent = value.contentBlockDelta.delta.reasoningContent;
              if ("text" in reasoningContent && reasoningContent.text) {
                controller.enqueue({
                  type: "reasoning",
                  textDelta: reasoningContent.text
                });
              } else if ("signature" in reasoningContent && reasoningContent.signature) {
                controller.enqueue({
                  type: "reasoning-signature",
                  signature: reasoningContent.signature
                });
              } else if ("data" in reasoningContent && reasoningContent.data) {
                controller.enqueue({
                  type: "redacted-reasoning",
                  data: reasoningContent.data
                });
              }
            }
            const contentBlockStart = value.contentBlockStart;
            if (((_m = contentBlockStart == null ? void 0 : contentBlockStart.start) == null ? void 0 : _m.toolUse) != null) {
              const toolUse = contentBlockStart.start.toolUse;
              toolCallContentBlocks[contentBlockStart.contentBlockIndex] = {
                toolCallId: toolUse.toolUseId,
                toolName: toolUse.name,
                jsonText: ""
              };
            }
            const contentBlockDelta = value.contentBlockDelta;
            if ((contentBlockDelta == null ? void 0 : contentBlockDelta.delta) && "toolUse" in contentBlockDelta.delta && contentBlockDelta.delta.toolUse) {
              const contentBlock = toolCallContentBlocks[contentBlockDelta.contentBlockIndex];
              const delta = (_n = contentBlockDelta.delta.toolUse.input) != null ? _n : "";
              controller.enqueue({
                type: "tool-call-delta",
                toolCallType: "function",
                toolCallId: contentBlock.toolCallId,
                toolName: contentBlock.toolName,
                argsTextDelta: delta
              });
              contentBlock.jsonText += delta;
            }
            const contentBlockStop = value.contentBlockStop;
            if (contentBlockStop != null) {
              const index = contentBlockStop.contentBlockIndex;
              const contentBlock = toolCallContentBlocks[index];
              if (contentBlock != null) {
                controller.enqueue({
                  type: "tool-call",
                  toolCallType: "function",
                  toolCallId: contentBlock.toolCallId,
                  toolName: contentBlock.toolName,
                  args: contentBlock.jsonText
                });
                delete toolCallContentBlocks[index];
              }
            }
          },
          flush(controller) {
            controller.enqueue({
              type: "finish",
              finishReason,
              usage,
              ...providerMetadata && { providerMetadata }
            });
          }
        })
      ),
      rawCall: { rawPrompt, rawSettings },
      rawResponse: { headers: responseHeaders },
      warnings
    };
  }
  getUrl(modelId) {
    const encodedModelId = encodeURIComponent(modelId);
    return `${this.config.baseUrl()}/model/${encodedModelId}`;
  }
};
var BedrockReasoningConfigOptionsSchema = z2.object({
  type: z2.union([z2.literal("enabled"), z2.literal("disabled")]).nullish(),
  budget_tokens: z2.number().nullish(),
  budgetTokens: z2.number().nullish()
}).nullish();
var BedrockStopReasonSchema = z2.union([
  z2.enum(BEDROCK_STOP_REASONS),
  z2.string()
]);
var BedrockToolUseSchema = z2.object({
  toolUseId: z2.string(),
  name: z2.string(),
  input: z2.unknown()
});
var BedrockReasoningTextSchema = z2.object({
  signature: z2.string().nullish(),
  text: z2.string()
});
var BedrockRedactedReasoningSchema = z2.object({
  data: z2.string()
});
var BedrockResponseSchema = z2.object({
  metrics: z2.object({
    latencyMs: z2.number()
  }).nullish(),
  output: z2.object({
    message: z2.object({
      content: z2.array(
        z2.object({
          text: z2.string().nullish(),
          toolUse: BedrockToolUseSchema.nullish(),
          reasoningContent: z2.union([
            z2.object({
              reasoningText: BedrockReasoningTextSchema
            }),
            z2.object({
              redactedReasoning: BedrockRedactedReasoningSchema
            })
          ]).nullish()
        })
      ),
      role: z2.string()
    })
  }),
  stopReason: BedrockStopReasonSchema,
  trace: z2.unknown().nullish(),
  usage: z2.object({
    inputTokens: z2.number(),
    outputTokens: z2.number(),
    totalTokens: z2.number(),
    cacheReadInputTokens: z2.number().nullish(),
    cacheWriteInputTokens: z2.number().nullish()
  })
});
var BedrockStreamSchema = z2.object({
  contentBlockDelta: z2.object({
    contentBlockIndex: z2.number(),
    delta: z2.union([
      z2.object({ text: z2.string() }),
      z2.object({ toolUse: z2.object({ input: z2.string() }) }),
      z2.object({
        reasoningContent: z2.object({ text: z2.string() })
      }),
      z2.object({
        reasoningContent: z2.object({
          signature: z2.string()
        })
      }),
      z2.object({
        reasoningContent: z2.object({ data: z2.string() })
      })
    ]).nullish()
  }).nullish(),
  contentBlockStart: z2.object({
    contentBlockIndex: z2.number(),
    start: z2.object({
      toolUse: BedrockToolUseSchema.nullish()
    }).nullish()
  }).nullish(),
  contentBlockStop: z2.object({
    contentBlockIndex: z2.number()
  }).nullish(),
  internalServerException: z2.record(z2.unknown()).nullish(),
  messageStop: z2.object({
    additionalModelResponseFields: z2.record(z2.unknown()).nullish(),
    stopReason: BedrockStopReasonSchema
  }).nullish(),
  metadata: z2.object({
    trace: z2.unknown().nullish(),
    usage: z2.object({
      cacheReadInputTokens: z2.number().nullish(),
      cacheWriteInputTokens: z2.number().nullish(),
      inputTokens: z2.number(),
      outputTokens: z2.number()
    }).nullish()
  }).nullish(),
  modelStreamErrorException: z2.record(z2.unknown()).nullish(),
  throttlingException: z2.record(z2.unknown()).nullish(),
  validationException: z2.record(z2.unknown()).nullish()
});

// src/bedrock-embedding-model.ts
import {
  combineHeaders as combineHeaders2,
  createJsonErrorResponseHandler as createJsonErrorResponseHandler2,
  createJsonResponseHandler as createJsonResponseHandler2,
  postJsonToApi as postJsonToApi2,
  resolve as resolve2
} from "@ai-sdk/provider-utils";
import { z as z3 } from "zod";
var BedrockEmbeddingModel = class {
  constructor(modelId, settings, config) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
    this.specificationVersion = "v1";
    this.provider = "amazon-bedrock";
    this.maxEmbeddingsPerCall = void 0;
    this.supportsParallelCalls = true;
  }
  getUrl(modelId) {
    const encodedModelId = encodeURIComponent(modelId);
    return `${this.config.baseUrl()}/model/${encodedModelId}/invoke`;
  }
  async doEmbed({
    values,
    headers,
    abortSignal
  }) {
    const embedSingleText = async (inputText) => {
      const args = {
        inputText,
        dimensions: this.settings.dimensions,
        normalize: this.settings.normalize
      };
      const url = this.getUrl(this.modelId);
      const { value: response } = await postJsonToApi2({
        url,
        headers: await resolve2(
          combineHeaders2(await resolve2(this.config.headers), headers)
        ),
        body: args,
        failedResponseHandler: createJsonErrorResponseHandler2({
          errorSchema: BedrockErrorSchema,
          errorToMessage: (error) => `${error.type}: ${error.message}`
        }),
        successfulResponseHandler: createJsonResponseHandler2(
          BedrockEmbeddingResponseSchema
        ),
        fetch: this.config.fetch,
        abortSignal
      });
      return {
        embedding: response.embedding,
        inputTextTokenCount: response.inputTextTokenCount
      };
    };
    const responses = await Promise.all(values.map(embedSingleText));
    return responses.reduce(
      (accumulated, response) => {
        accumulated.embeddings.push(response.embedding);
        accumulated.usage.tokens += response.inputTextTokenCount;
        return accumulated;
      },
      { embeddings: [], usage: { tokens: 0 } }
    );
  }
};
var BedrockEmbeddingResponseSchema = z3.object({
  embedding: z3.array(z3.number()),
  inputTextTokenCount: z3.number()
});

// src/bedrock-image-model.ts
import {
  combineHeaders as combineHeaders3,
  createJsonErrorResponseHandler as createJsonErrorResponseHandler3,
  createJsonResponseHandler as createJsonResponseHandler3,
  postJsonToApi as postJsonToApi3,
  resolve as resolve3
} from "@ai-sdk/provider-utils";

// src/bedrock-image-settings.ts
var modelMaxImagesPerCall = {
  "amazon.nova-canvas-v1:0": 5
};

// src/bedrock-image-model.ts
import { z as z4 } from "zod";
var BedrockImageModel = class {
  constructor(modelId, settings, config) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
    this.specificationVersion = "v1";
    this.provider = "amazon-bedrock";
  }
  get maxImagesPerCall() {
    var _a, _b;
    return (_b = (_a = this.settings.maxImagesPerCall) != null ? _a : modelMaxImagesPerCall[this.modelId]) != null ? _b : 1;
  }
  getUrl(modelId) {
    const encodedModelId = encodeURIComponent(modelId);
    return `${this.config.baseUrl()}/model/${encodedModelId}/invoke`;
  }
  async doGenerate({
    prompt,
    n,
    size,
    aspectRatio,
    seed,
    providerOptions,
    headers,
    abortSignal
  }) {
    var _a, _b, _c, _d, _e, _f;
    const warnings = [];
    const [width, height] = size ? size.split("x").map(Number) : [];
    const args = {
      taskType: "TEXT_IMAGE",
      textToImageParams: {
        text: prompt,
        ...((_a = providerOptions == null ? void 0 : providerOptions.bedrock) == null ? void 0 : _a.negativeText) ? {
          negativeText: providerOptions.bedrock.negativeText
        } : {}
      },
      imageGenerationConfig: {
        ...width ? { width } : {},
        ...height ? { height } : {},
        ...seed ? { seed } : {},
        ...n ? { numberOfImages: n } : {},
        ...((_b = providerOptions == null ? void 0 : providerOptions.bedrock) == null ? void 0 : _b.quality) ? { quality: providerOptions.bedrock.quality } : {},
        ...((_c = providerOptions == null ? void 0 : providerOptions.bedrock) == null ? void 0 : _c.cfgScale) ? { cfgScale: providerOptions.bedrock.cfgScale } : {}
      }
    };
    if (aspectRatio != void 0) {
      warnings.push({
        type: "unsupported-setting",
        setting: "aspectRatio",
        details: "This model does not support aspect ratio. Use `size` instead."
      });
    }
    const currentDate = (_f = (_e = (_d = this.config._internal) == null ? void 0 : _d.currentDate) == null ? void 0 : _e.call(_d)) != null ? _f : /* @__PURE__ */ new Date();
    const { value: response, responseHeaders } = await postJsonToApi3({
      url: this.getUrl(this.modelId),
      headers: await resolve3(
        combineHeaders3(await resolve3(this.config.headers), headers)
      ),
      body: args,
      failedResponseHandler: createJsonErrorResponseHandler3({
        errorSchema: BedrockErrorSchema,
        errorToMessage: (error) => `${error.type}: ${error.message}`
      }),
      successfulResponseHandler: createJsonResponseHandler3(
        bedrockImageResponseSchema
      ),
      abortSignal,
      fetch: this.config.fetch
    });
    return {
      images: response.images,
      warnings,
      response: {
        timestamp: currentDate,
        modelId: this.modelId,
        headers: responseHeaders
      }
    };
  }
};
var bedrockImageResponseSchema = z4.object({
  images: z4.array(z4.string())
});

// src/headers-utils.ts
function extractHeaders(headers) {
  let originalHeaders = {};
  if (headers) {
    if (headers instanceof Headers) {
      originalHeaders = convertHeadersToRecord(headers);
    } else if (Array.isArray(headers)) {
      for (const [k, v] of headers) {
        originalHeaders[k.toLowerCase()] = v;
      }
    } else {
      originalHeaders = Object.fromEntries(
        Object.entries(headers).map(([k, v]) => [k.toLowerCase(), v])
      );
    }
  }
  return originalHeaders;
}
function convertHeadersToRecord(headers) {
  const record = {};
  headers.forEach((value, key) => {
    record[key.toLowerCase()] = value;
  });
  return record;
}

// src/bedrock-sigv4-fetch.ts
import {
  combineHeaders as combineHeaders4,
  removeUndefinedEntries
} from "@ai-sdk/provider-utils";
import { AwsV4Signer } from "aws4fetch";
function createSigV4FetchFunction(getCredentials, fetch = globalThis.fetch) {
  return async (input, init) => {
    var _a;
    if (((_a = init == null ? void 0 : init.method) == null ? void 0 : _a.toUpperCase()) !== "POST" || !(init == null ? void 0 : init.body)) {
      return fetch(input, init);
    }
    const url = typeof input === "string" ? input : input instanceof URL ? input.href : input.url;
    const originalHeaders = extractHeaders(init.headers);
    const body = prepareBodyString(init.body);
    const credentials = await getCredentials();
    const signer = new AwsV4Signer({
      url,
      method: "POST",
      headers: Object.entries(removeUndefinedEntries(originalHeaders)),
      body,
      region: credentials.region,
      accessKeyId: credentials.accessKeyId,
      secretAccessKey: credentials.secretAccessKey,
      sessionToken: credentials.sessionToken,
      service: "bedrock"
    });
    const signingResult = await signer.sign();
    const signedHeaders = convertHeadersToRecord(signingResult.headers);
    return fetch(input, {
      ...init,
      body,
      headers: removeUndefinedEntries(
        combineHeaders4(originalHeaders, signedHeaders)
      )
    });
  };
}
function prepareBodyString(body) {
  if (typeof body === "string") {
    return body;
  } else if (body instanceof Uint8Array) {
    return new TextDecoder().decode(body);
  } else if (body instanceof ArrayBuffer) {
    return new TextDecoder().decode(new Uint8Array(body));
  } else {
    return JSON.stringify(body);
  }
}

// src/bedrock-provider.ts
function createAmazonBedrock(options = {}) {
  const sigv4Fetch = createSigV4FetchFunction(async () => {
    const region = loadSetting({
      settingValue: options.region,
      settingName: "region",
      environmentVariableName: "AWS_REGION",
      description: "AWS region"
    });
    if (options.credentialProvider) {
      return {
        ...await options.credentialProvider(),
        region
      };
    }
    return {
      region,
      accessKeyId: loadSetting({
        settingValue: options.accessKeyId,
        settingName: "accessKeyId",
        environmentVariableName: "AWS_ACCESS_KEY_ID",
        description: "AWS access key ID"
      }),
      secretAccessKey: loadSetting({
        settingValue: options.secretAccessKey,
        settingName: "secretAccessKey",
        environmentVariableName: "AWS_SECRET_ACCESS_KEY",
        description: "AWS secret access key"
      }),
      sessionToken: loadOptionalSetting({
        settingValue: options.sessionToken,
        environmentVariableName: "AWS_SESSION_TOKEN"
      })
    };
  }, options.fetch);
  const getBaseUrl = () => {
    var _a, _b;
    return (_b = withoutTrailingSlash(
      (_a = options.baseURL) != null ? _a : `https://bedrock-runtime.${loadSetting({
        settingValue: options.region,
        settingName: "region",
        environmentVariableName: "AWS_REGION",
        description: "AWS region"
      })}.amazonaws.com`
    )) != null ? _b : `https://bedrock-runtime.us-east-1.amazonaws.com`;
  };
  const createChatModel = (modelId, settings = {}) => {
    var _a;
    return new BedrockChatLanguageModel(modelId, settings, {
      baseUrl: getBaseUrl,
      headers: (_a = options.headers) != null ? _a : {},
      fetch: sigv4Fetch,
      generateId
    });
  };
  const provider = function(modelId, settings) {
    if (new.target) {
      throw new Error(
        "The Amazon Bedrock model function cannot be called with the new keyword."
      );
    }
    return createChatModel(modelId, settings);
  };
  const createEmbeddingModel = (modelId, settings = {}) => {
    var _a;
    return new BedrockEmbeddingModel(modelId, settings, {
      baseUrl: getBaseUrl,
      headers: (_a = options.headers) != null ? _a : {},
      fetch: sigv4Fetch
    });
  };
  const createImageModel = (modelId, settings = {}) => {
    var _a;
    return new BedrockImageModel(modelId, settings, {
      baseUrl: getBaseUrl,
      headers: (_a = options.headers) != null ? _a : {},
      fetch: sigv4Fetch
    });
  };
  provider.languageModel = createChatModel;
  provider.embedding = createEmbeddingModel;
  provider.textEmbedding = createEmbeddingModel;
  provider.textEmbeddingModel = createEmbeddingModel;
  provider.image = createImageModel;
  provider.imageModel = createImageModel;
  return provider;
}
var bedrock = createAmazonBedrock();
export {
  bedrock,
  createAmazonBedrock
};
//# sourceMappingURL=index.mjs.map