{"version": 3, "sources": ["../../src/edge/index.ts", "../../src/edge/google-vertex-provider-edge.ts", "../../src/google-vertex-provider.ts", "../../src/google-vertex-embedding-model.ts", "../../src/google-vertex-error.ts", "../../src/google-vertex-image-model.ts", "../../src/google-vertex-supported-file-url.ts", "../../src/edge/google-vertex-auth-edge.ts"], "sourcesContent": ["export { createVertex, vertex } from './google-vertex-provider-edge';\nexport type {\n  GoogleVertexProviderSettings,\n  GoogleVertexProvider,\n} from './google-vertex-provider-edge';\n", "import { resolve } from '@ai-sdk/provider-utils';\nimport {\n  createVertex as createVertexOriginal,\n  GoogleVertexProvider,\n  GoogleVertexProviderSettings as GoogleVertexProviderSettingsOriginal,\n} from '../google-vertex-provider';\nimport {\n  generateAuthToken,\n  GoogleCredentials,\n} from './google-vertex-auth-edge';\n\nexport type { GoogleVertexProvider };\n\nexport interface GoogleVertexProviderSettings\n  extends GoogleVertexProviderSettingsOriginal {\n  /**\n   * Optional. The Google credentials for the Google Cloud service account. If\n   * not provided, the Google Vertex provider will use environment variables to\n   * load the credentials.\n   */\n  googleCredentials?: GoogleCredentials;\n}\n\nexport function createVertex(\n  options: GoogleVertexProviderSettings = {},\n): GoogleVertexProvider {\n  return createVertexOriginal({\n    ...options,\n    headers: async () => ({\n      Authorization: `Bearer ${await generateAuthToken(\n        options.googleCredentials,\n      )}`,\n      ...(await resolve(options.headers)),\n    }),\n  });\n}\n\n/**\nDefault Google Vertex AI provider instance.\n */\nexport const vertex = createVertex();\n", "import { LanguageModelV1, ProviderV1, ImageModelV1 } from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  generateId,\n  loadSetting,\n  Resolvable,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport {\n  GoogleVertexModelId,\n  GoogleVertexSettings,\n} from './google-vertex-settings';\nimport {\n  GoogleVertexEmbeddingModelId,\n  GoogleVertexEmbeddingSettings,\n} from './google-vertex-embedding-settings';\nimport { GoogleVertexEmbeddingModel } from './google-vertex-embedding-model';\nimport { GoogleGenerativeAILanguageModel } from '@ai-sdk/google/internal';\nimport { GoogleVertexImageModel } from './google-vertex-image-model';\nimport {\n  GoogleVertexImageModelId,\n  GoogleVertexImageSettings,\n} from './google-vertex-image-settings';\nimport { GoogleVertexConfig } from './google-vertex-config';\nimport { isSupportedFileUrl } from './google-vertex-supported-file-url';\n\nexport interface GoogleVertexProvider extends ProviderV1 {\n  /**\nCreates a model for text generation.\n   */\n  (\n    modelId: GoogleVertexModelId,\n    settings?: GoogleVertexSettings,\n  ): LanguageModelV1;\n\n  languageModel: (\n    modelId: GoogleVertexModelId,\n    settings?: GoogleVertexSettings,\n  ) => LanguageModelV1;\n\n  /**\n   * Creates a model for image generation.\n   */\n  image(\n    modelId: GoogleVertexImageModelId,\n    settings?: GoogleVertexImageSettings,\n  ): ImageModelV1;\n\n  /**\nCreates a model for image generation.\n   */\n  imageModel(\n    modelId: GoogleVertexImageModelId,\n    settings?: GoogleVertexImageSettings,\n  ): ImageModelV1;\n}\n\nexport interface GoogleVertexProviderSettings {\n  /**\nYour Google Vertex location. Defaults to the environment variable `GOOGLE_VERTEX_LOCATION`.\n   */\n  location?: string;\n\n  /**\nYour Google Vertex project. Defaults to the environment variable `GOOGLE_VERTEX_PROJECT`.\n  */\n  project?: string;\n\n  /**\n   * Headers to use for requests. Can be:\n   * - A headers object\n   * - A Promise that resolves to a headers object\n   * - A function that returns a headers object\n   * - A function that returns a Promise of a headers object\n   */\n  headers?: Resolvable<Record<string, string | undefined>>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  // for testing\n  generateId?: () => string;\n\n  /**\nBase URL for the Google Vertex API calls.\n     */\n  baseURL?: string;\n}\n\n/**\nCreate a Google Vertex AI provider instance.\n */\nexport function createVertex(\n  options: GoogleVertexProviderSettings = {},\n): GoogleVertexProvider {\n  const loadVertexProject = () =>\n    loadSetting({\n      settingValue: options.project,\n      settingName: 'project',\n      environmentVariableName: 'GOOGLE_VERTEX_PROJECT',\n      description: 'Google Vertex project',\n    });\n\n  const loadVertexLocation = () =>\n    loadSetting({\n      settingValue: options.location,\n      settingName: 'location',\n      environmentVariableName: 'GOOGLE_VERTEX_LOCATION',\n      description: 'Google Vertex location',\n    });\n\n  const loadBaseURL = () => {\n    const region = loadVertexLocation();\n    const project = loadVertexProject();\n    return (\n      withoutTrailingSlash(options.baseURL) ??\n      `https://${region}-aiplatform.googleapis.com/v1/projects/${project}/locations/${region}/publishers/google`\n    );\n  };\n\n  const createConfig = (name: string): GoogleVertexConfig => {\n    return {\n      provider: `google.vertex.${name}`,\n      headers: options.headers ?? {},\n      fetch: options.fetch,\n      baseURL: loadBaseURL(),\n    };\n  };\n\n  const createChatModel = (\n    modelId: GoogleVertexModelId,\n    settings: GoogleVertexSettings = {},\n  ) => {\n    return new GoogleGenerativeAILanguageModel(modelId, settings, {\n      ...createConfig('chat'),\n      generateId: options.generateId ?? generateId,\n      isSupportedUrl: isSupportedFileUrl,\n    });\n  };\n\n  const createEmbeddingModel = (\n    modelId: GoogleVertexEmbeddingModelId,\n    settings: GoogleVertexEmbeddingSettings = {},\n  ) =>\n    new GoogleVertexEmbeddingModel(\n      modelId,\n      settings,\n      createConfig('embedding'),\n    );\n\n  const createImageModel = (\n    modelId: GoogleVertexImageModelId,\n    settings: GoogleVertexImageSettings = {},\n  ) => new GoogleVertexImageModel(modelId, settings, createConfig('image'));\n\n  const provider = function (\n    modelId: GoogleVertexModelId,\n    settings?: GoogleVertexSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Google Vertex AI model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n\n  return provider;\n}\n", "import {\n  EmbeddingModelV1,\n  TooManyEmbeddingValuesForCallError,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  postJsonToApi,\n  resolve,\n  Resolvable,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { googleVertexFailedResponseHandler } from './google-vertex-error';\nimport {\n  GoogleVertexEmbeddingModelId,\n  GoogleVertexEmbeddingSettings,\n} from './google-vertex-embedding-settings';\nimport { GoogleVertexConfig } from './google-vertex-config';\n\nexport class GoogleVertexEmbeddingModel implements EmbeddingModelV1<string> {\n  readonly specificationVersion = 'v1';\n  readonly modelId: GoogleVertexEmbeddingModelId;\n\n  private readonly config: GoogleVertexConfig;\n  private readonly settings: GoogleVertexEmbeddingSettings;\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxEmbeddingsPerCall(): number {\n    return 2048;\n  }\n\n  get supportsParallelCalls(): boolean {\n    return true;\n  }\n\n  constructor(\n    modelId: GoogleVertexEmbeddingModelId,\n    settings: GoogleVertexEmbeddingSettings,\n    config: GoogleVertexConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n  }: Parameters<EmbeddingModelV1<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>\n  > {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values,\n      });\n    }\n\n    const mergedHeaders = combineHeaders(\n      await resolve(this.config.headers),\n      headers,\n    );\n\n    const url = `${this.config.baseURL}/models/${this.modelId}:predict`;\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url,\n      headers: mergedHeaders,\n      body: {\n        instances: values.map(value => ({ content: value })),\n        parameters: {\n          outputDimensionality: this.settings.outputDimensionality,\n        },\n      },\n      failedResponseHandler: googleVertexFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        googleVertexTextEmbeddingResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      embeddings: response.predictions.map(\n        prediction => prediction.embeddings.values,\n      ),\n      usage: {\n        tokens: response.predictions.reduce(\n          (tokenCount, prediction) =>\n            tokenCount + prediction.embeddings.statistics.token_count,\n          0,\n        ),\n      },\n      rawResponse: { headers: responseHeaders },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst googleVertexTextEmbeddingResponseSchema = z.object({\n  predictions: z.array(\n    z.object({\n      embeddings: z.object({\n        values: z.array(z.number()),\n        statistics: z.object({\n          token_count: z.number(),\n        }),\n      }),\n    }),\n  ),\n});\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst googleVertexErrorDataSchema = z.object({\n  error: z.object({\n    code: z.number().nullable(),\n    message: z.string(),\n    status: z.string(),\n  }),\n});\n\nexport type GoogleVertexErrorData = z.infer<typeof googleVertexErrorDataSchema>;\n\nexport const googleVertexFailedResponseHandler = createJsonErrorResponseHandler(\n  {\n    errorSchema: googleVertexErrorDataSchema,\n    errorToMessage: data => data.error.message,\n  },\n);\n", "import { ImageModelV1, ImageModelV1CallWarning } from '@ai-sdk/provider';\nimport {\n  Resolvable,\n  combineHeaders,\n  createJsonResponseHandler,\n  parseProviderOptions,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { googleVertexFailedResponseHandler } from './google-vertex-error';\nimport {\n  GoogleVertexImageModelId,\n  GoogleVertexImageSettings,\n} from './google-vertex-image-settings';\n\ninterface GoogleVertexImageModelConfig {\n  provider: string;\n  baseURL: string;\n  headers?: Resolvable<Record<string, string | undefined>>;\n  fetch?: typeof fetch;\n  _internal?: {\n    currentDate?: () => Date;\n  };\n}\n\n// https://cloud.google.com/vertex-ai/generative-ai/docs/image/generate-images\nexport class GoogleVertexImageModel implements ImageModelV1 {\n  readonly specificationVersion = 'v1';\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxImagesPerCall(): number {\n    // https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/imagen-api#parameter_list\n    return this.settings.maxImagesPerCall ?? 4;\n  }\n\n  constructor(\n    readonly modelId: GoogleVertexImageModelId,\n    readonly settings: GoogleVertexImageSettings,\n    private config: GoogleVertexImageModelConfig,\n  ) {}\n\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    aspectRatio,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal,\n  }: Parameters<ImageModelV1['doGenerate']>[0]): Promise<\n    Awaited<ReturnType<ImageModelV1['doGenerate']>>\n  > {\n    const warnings: Array<ImageModelV1CallWarning> = [];\n\n    if (size != null) {\n      warnings.push({\n        type: 'unsupported-setting',\n        setting: 'size',\n        details:\n          'This model does not support the `size` option. Use `aspectRatio` instead.',\n      });\n    }\n\n    const vertexImageOptions = parseProviderOptions({\n      provider: 'vertex',\n      providerOptions,\n      schema: vertexImageProviderOptionsSchema,\n    });\n\n    const body = {\n      instances: [{ prompt }],\n      parameters: {\n        sampleCount: n,\n        ...(aspectRatio != null ? { aspectRatio } : {}),\n        ...(seed != null ? { seed } : {}),\n        ...(vertexImageOptions ?? {}),\n      },\n    };\n\n    const currentDate = this.config._internal?.currentDate?.() ?? new Date();\n    const { value: response, responseHeaders } = await postJsonToApi({\n      url: `${this.config.baseURL}/models/${this.modelId}:predict`,\n      headers: combineHeaders(await resolve(this.config.headers), headers),\n      body,\n      failedResponseHandler: googleVertexFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        vertexImageResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      images:\n        response.predictions?.map(\n          (p: { bytesBase64Encoded: string }) => p.bytesBase64Encoded,\n        ) ?? [],\n      warnings,\n      response: {\n        timestamp: currentDate,\n        modelId: this.modelId,\n        headers: responseHeaders,\n      },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst vertexImageResponseSchema = z.object({\n  predictions: z.array(z.object({ bytesBase64Encoded: z.string() })).nullish(),\n});\n\nconst vertexImageProviderOptionsSchema = z.object({\n  negativePrompt: z.string().nullish(),\n  personGeneration: z\n    .enum(['dont_allow', 'allow_adult', 'allow_all'])\n    .nullish(),\n  safetySetting: z\n    .enum([\n      'block_low_and_above',\n      'block_medium_and_above',\n      'block_only_high',\n      'block_none',\n    ])\n    .nullish(),\n  addWatermark: z.boolean().nullish(),\n  storageUri: z.string().nullish(),\n});\nexport type GoogleVertexImageProviderOptions = z.infer<\n  typeof vertexImageProviderOptionsSchema\n>;\n", "// https://firebase.google.com/docs/vertex-ai/input-file-requirements\n// The definition of supported file URLs reduces to a simple protocol check as\n// any publicly accessible file can be used as input.\nexport function isSupportedFileUrl(url: URL) {\n  return ['http:', 'https:', 'gs:'].includes(url.protocol);\n}\n", "import { loadOptionalSetting, loadSetting } from '@ai-sdk/provider-utils';\n\nexport interface GoogleCredentials {\n  /**\n   * The client email for the Google Cloud service account. Defaults to the\n   * value of the `GOOGLE_CLIENT_EMAIL` environment variable.\n   */\n  clientEmail: string;\n\n  /**\n   * The private key for the Google Cloud service account. Defaults to the\n   * value of the `GOOGLE_PRIVATE_KEY` environment variable.\n   */\n  privateKey: string;\n\n  /**\n   * Optional. The private key ID for the Google Cloud service account. Defaults\n   * to the value of the `GOOGLE_PRIVATE_KEY_ID` environment variable.\n   */\n  privateKeyId?: string;\n}\n\nconst loadCredentials = async (): Promise<GoogleCredentials> => {\n  try {\n    return {\n      clientEmail: loadSetting({\n        settingValue: undefined,\n        settingName: 'clientEmail',\n        environmentVariableName: 'GOOGLE_CLIENT_EMAIL',\n        description: 'Google client email',\n      }),\n      privateKey: loadSetting({\n        settingValue: undefined,\n        settingName: 'privateKey',\n        environmentVariableName: 'GOOGLE_PRIVATE_KEY',\n        description: 'Google private key',\n      }),\n      privateKeyId: loadOptionalSetting({\n        settingValue: undefined,\n        environmentVariableName: 'GOOGLE_PRIVATE_KEY_ID',\n      }),\n    };\n  } catch (error: any) {\n    throw new Error(`Failed to load Google credentials: ${error.message}`);\n  }\n};\n\n// Convert a string to base64url\nconst base64url = (str: string) => {\n  return btoa(str).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n};\nconst importPrivateKey = async (pemKey: string) => {\n  const pemHeader = '-----BEGIN PRIVATE KEY-----';\n  const pemFooter = '-----END PRIVATE KEY-----';\n\n  // Remove header, footer, and any whitespace/newlines\n  const pemContents = pemKey\n    .replace(pemHeader, '')\n    .replace(pemFooter, '')\n    .replace(/\\s/g, '');\n\n  // Decode base64 to binary\n  const binaryString = atob(pemContents);\n\n  // Convert binary string to Uint8Array\n  const binaryData = new Uint8Array(binaryString.length);\n  for (let i = 0; i < binaryString.length; i++) {\n    binaryData[i] = binaryString.charCodeAt(i);\n  }\n\n  return await crypto.subtle.importKey(\n    'pkcs8',\n    binaryData,\n    { name: 'RSASSA-PKCS1-v1_5', hash: 'SHA-256' },\n    true,\n    ['sign'],\n  );\n};\n\nconst buildJwt = async (credentials: GoogleCredentials) => {\n  const now = Math.floor(Date.now() / 1000);\n\n  // Only include kid in header if privateKeyId is provided\n  const header: { alg: string; typ: string; kid?: string } = {\n    alg: 'RS256',\n    typ: 'JWT',\n  };\n\n  if (credentials.privateKeyId) {\n    header.kid = credentials.privateKeyId;\n  }\n\n  const payload = {\n    iss: credentials.clientEmail,\n    scope: 'https://www.googleapis.com/auth/cloud-platform',\n    aud: 'https://oauth2.googleapis.com/token',\n    exp: now + 3600,\n    iat: now,\n  };\n\n  const privateKey = await importPrivateKey(credentials.privateKey);\n\n  const signingInput = `${base64url(JSON.stringify(header))}.${base64url(\n    JSON.stringify(payload),\n  )}`;\n  const encoder = new TextEncoder();\n  const data = encoder.encode(signingInput);\n\n  const signature = await crypto.subtle.sign(\n    'RSASSA-PKCS1-v1_5',\n    privateKey,\n    data,\n  );\n\n  const signatureBase64 = base64url(\n    String.fromCharCode(...new Uint8Array(signature)),\n  );\n\n  return `${base64url(JSON.stringify(header))}.${base64url(\n    JSON.stringify(payload),\n  )}.${signatureBase64}`;\n};\n\n/**\n * Generate an authentication token for Google Vertex AI in a manner compatible\n * with the Edge runtime.\n */\nexport async function generateAuthToken(credentials?: GoogleCredentials) {\n  try {\n    const creds = credentials || (await loadCredentials());\n    const jwt = await buildJwt(creds);\n\n    const response = await fetch('https://oauth2.googleapis.com/token', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n      body: new URLSearchParams({\n        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',\n        assertion: jwt,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Token request failed: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.access_token;\n  } catch (error) {\n    throw error;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,sBAAAA;AAAA,EAAA;AAAA;AAAA;;;ACAA,IAAAC,yBAAwB;;;ACCxB,IAAAC,yBAMO;;;ACPP,sBAGO;AACP,IAAAC,yBAMO;AACP,IAAAC,cAAkB;;;ACXlB,4BAA+C;AAC/C,iBAAkB;AAElB,IAAM,8BAA8B,aAAE,OAAO;AAAA,EAC3C,OAAO,aAAE,OAAO;AAAA,IACd,MAAM,aAAE,OAAO,EAAE,SAAS;AAAA,IAC1B,SAAS,aAAE,OAAO;AAAA,IAClB,QAAQ,aAAE,OAAO;AAAA,EACnB,CAAC;AACH,CAAC;AAIM,IAAM,wCAAoC;AAAA,EAC/C;AAAA,IACE,aAAa;AAAA,IACb,gBAAgB,UAAQ,KAAK,MAAM;AAAA,EACrC;AACF;;;ADCO,IAAM,6BAAN,MAAqE;AAAA,EAmB1E,YACE,SACA,UACA,QACA;AAtBF,SAAS,uBAAuB;AAuB9B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EApBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,uBAA+B;AACjC,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,wBAAiC;AACnC,WAAO;AAAA,EACT;AAAA,EAYA,MAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEE;AACA,QAAI,OAAO,SAAS,KAAK,sBAAsB;AAC7C,YAAM,IAAI,mDAAmC;AAAA,QAC3C,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,QACd,sBAAsB,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,oBAAgB;AAAA,MACpB,UAAM,gCAAQ,KAAK,OAAO,OAAO;AAAA,MACjC;AAAA,IACF;AAEA,UAAM,MAAM,GAAG,KAAK,OAAO,OAAO,WAAW,KAAK,OAAO;AACzD,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D;AAAA,MACA,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,WAAW,OAAO,IAAI,YAAU,EAAE,SAAS,MAAM,EAAE;AAAA,QACnD,YAAY;AAAA,UACV,sBAAsB,KAAK,SAAS;AAAA,QACtC;AAAA,MACF;AAAA,MACA,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA;AAAA,MACA,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,YAAY,SAAS,YAAY;AAAA,QAC/B,gBAAc,WAAW,WAAW;AAAA,MACtC;AAAA,MACA,OAAO;AAAA,QACL,QAAQ,SAAS,YAAY;AAAA,UAC3B,CAAC,YAAY,eACX,aAAa,WAAW,WAAW,WAAW;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,MACA,aAAa,EAAE,SAAS,gBAAgB;AAAA,IAC1C;AAAA,EACF;AACF;AAIA,IAAM,0CAA0C,cAAE,OAAO;AAAA,EACvD,aAAa,cAAE;AAAA,IACb,cAAE,OAAO;AAAA,MACP,YAAY,cAAE,OAAO;AAAA,QACnB,QAAQ,cAAE,MAAM,cAAE,OAAO,CAAC;AAAA,QAC1B,YAAY,cAAE,OAAO;AAAA,UACnB,aAAa,cAAE,OAAO;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;;;ADnGD,sBAAgD;;;AGhBhD,IAAAC,yBAOO;AACP,IAAAC,cAAkB;AAkBX,IAAM,yBAAN,MAAqD;AAAA,EAY1D,YACW,SACA,UACD,QACR;AAHS;AACA;AACD;AAdV,SAAS,uBAAuB;AAAA,EAe7B;AAAA,EAbH,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,mBAA2B;AAlCjC;AAoCI,YAAO,UAAK,SAAS,qBAAd,YAAkC;AAAA,EAC3C;AAAA,EAQA,MAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEE;AAxDJ;AAyDI,UAAM,WAA2C,CAAC;AAElD,QAAI,QAAQ,MAAM;AAChB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SACE;AAAA,MACJ,CAAC;AAAA,IACH;AAEA,UAAM,yBAAqB,6CAAqB;AAAA,MAC9C,UAAU;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,IACV,CAAC;AAED,UAAM,OAAO;AAAA,MACX,WAAW,CAAC,EAAE,OAAO,CAAC;AAAA,MACtB,YAAY;AAAA,QACV,aAAa;AAAA,QACb,GAAI,eAAe,OAAO,EAAE,YAAY,IAAI,CAAC;AAAA,QAC7C,GAAI,QAAQ,OAAO,EAAE,KAAK,IAAI,CAAC;AAAA,QAC/B,GAAI,kDAAsB,CAAC;AAAA,MAC7B;AAAA,IACF;AAEA,UAAM,eAAc,sBAAK,OAAO,cAAZ,mBAAuB,gBAAvB,4CAA0C,oBAAI,KAAK;AACvE,UAAM,EAAE,OAAO,UAAU,gBAAgB,IAAI,UAAM,sCAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,WAAW,KAAK,OAAO;AAAA,MAClD,aAAS,uCAAe,UAAM,gCAAQ,KAAK,OAAO,OAAO,GAAG,OAAO;AAAA,MACnE;AAAA,MACA,uBAAuB;AAAA,MACvB,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA;AAAA,MACA,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,SACE,oBAAS,gBAAT,mBAAsB;AAAA,QACpB,CAAC,MAAsC,EAAE;AAAA,YAD3C,YAEK,CAAC;AAAA,MACR;AAAA,MACA,UAAU;AAAA,QACR,WAAW;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,4BAA4B,cAAE,OAAO;AAAA,EACzC,aAAa,cAAE,MAAM,cAAE,OAAO,EAAE,oBAAoB,cAAE,OAAO,EAAE,CAAC,CAAC,EAAE,QAAQ;AAC7E,CAAC;AAED,IAAM,mCAAmC,cAAE,OAAO;AAAA,EAChD,gBAAgB,cAAE,OAAO,EAAE,QAAQ;AAAA,EACnC,kBAAkB,cACf,KAAK,CAAC,cAAc,eAAe,WAAW,CAAC,EAC/C,QAAQ;AAAA,EACX,eAAe,cACZ,KAAK;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,EACA,QAAQ;AAAA,EACX,cAAc,cAAE,QAAQ,EAAE,QAAQ;AAAA,EAClC,YAAY,cAAE,OAAO,EAAE,QAAQ;AACjC,CAAC;;;AClIM,SAAS,mBAAmB,KAAU;AAC3C,SAAO,CAAC,SAAS,UAAU,KAAK,EAAE,SAAS,IAAI,QAAQ;AACzD;;;AJ0FO,SAAS,aACd,UAAwC,CAAC,GACnB;AACtB,QAAM,oBAAoB,UACxB,oCAAY;AAAA,IACV,cAAc,QAAQ;AAAA,IACtB,aAAa;AAAA,IACb,yBAAyB;AAAA,IACzB,aAAa;AAAA,EACf,CAAC;AAEH,QAAM,qBAAqB,UACzB,oCAAY;AAAA,IACV,cAAc,QAAQ;AAAA,IACtB,aAAa;AAAA,IACb,yBAAyB;AAAA,IACzB,aAAa;AAAA,EACf,CAAC;AAEH,QAAM,cAAc,MAAM;AAlH5B;AAmHI,UAAM,SAAS,mBAAmB;AAClC,UAAM,UAAU,kBAAkB;AAClC,YACE,sDAAqB,QAAQ,OAAO,MAApC,YACA,WAAW,MAAM,0CAA0C,OAAO,cAAc,MAAM;AAAA,EAE1F;AAEA,QAAM,eAAe,CAAC,SAAqC;AA3H7D;AA4HI,WAAO;AAAA,MACL,UAAU,iBAAiB,IAAI;AAAA,MAC/B,UAAS,aAAQ,YAAR,YAAmB,CAAC;AAAA,MAC7B,OAAO,QAAQ;AAAA,MACf,SAAS,YAAY;AAAA,IACvB;AAAA,EACF;AAEA,QAAM,kBAAkB,CACtB,SACA,WAAiC,CAAC,MAC/B;AAvIP;AAwII,WAAO,IAAI,gDAAgC,SAAS,UAAU;AAAA,MAC5D,GAAG,aAAa,MAAM;AAAA,MACtB,aAAY,aAAQ,eAAR,YAAsB;AAAA,MAClC,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAEA,QAAM,uBAAuB,CAC3B,SACA,WAA0C,CAAC,MAE3C,IAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA,aAAa,WAAW;AAAA,EAC1B;AAEF,QAAM,mBAAmB,CACvB,SACA,WAAsC,CAAC,MACpC,IAAI,uBAAuB,SAAS,UAAU,aAAa,OAAO,CAAC;AAExE,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAC9B,WAAS,QAAQ;AACjB,WAAS,aAAa;AAEtB,SAAO;AACT;;;AKjLA,IAAAC,yBAAiD;AAsBjD,IAAM,kBAAkB,YAAwC;AAC9D,MAAI;AACF,WAAO;AAAA,MACL,iBAAa,oCAAY;AAAA,QACvB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,MACD,gBAAY,oCAAY;AAAA,QACtB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,yBAAyB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,MACD,kBAAc,4CAAoB;AAAA,QAChC,cAAc;AAAA,QACd,yBAAyB;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF,SAAS,OAAY;AACnB,UAAM,IAAI,MAAM,sCAAsC,MAAM,OAAO,EAAE;AAAA,EACvE;AACF;AAGA,IAAM,YAAY,CAAC,QAAgB;AACjC,SAAO,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AAC3E;AACA,IAAM,mBAAmB,OAAO,WAAmB;AACjD,QAAM,YAAY;AAClB,QAAM,YAAY;AAGlB,QAAM,cAAc,OACjB,QAAQ,WAAW,EAAE,EACrB,QAAQ,WAAW,EAAE,EACrB,QAAQ,OAAO,EAAE;AAGpB,QAAM,eAAe,KAAK,WAAW;AAGrC,QAAM,aAAa,IAAI,WAAW,aAAa,MAAM;AACrD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,eAAW,CAAC,IAAI,aAAa,WAAW,CAAC;AAAA,EAC3C;AAEA,SAAO,MAAM,OAAO,OAAO;AAAA,IACzB;AAAA,IACA;AAAA,IACA,EAAE,MAAM,qBAAqB,MAAM,UAAU;AAAA,IAC7C;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AACF;AAEA,IAAM,WAAW,OAAO,gBAAmC;AACzD,QAAM,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAGxC,QAAM,SAAqD;AAAA,IACzD,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,MAAI,YAAY,cAAc;AAC5B,WAAO,MAAM,YAAY;AAAA,EAC3B;AAEA,QAAM,UAAU;AAAA,IACd,KAAK,YAAY;AAAA,IACjB,OAAO;AAAA,IACP,KAAK;AAAA,IACL,KAAK,MAAM;AAAA,IACX,KAAK;AAAA,EACP;AAEA,QAAM,aAAa,MAAM,iBAAiB,YAAY,UAAU;AAEhE,QAAM,eAAe,GAAG,UAAU,KAAK,UAAU,MAAM,CAAC,CAAC,IAAI;AAAA,IAC3D,KAAK,UAAU,OAAO;AAAA,EACxB,CAAC;AACD,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,OAAO,QAAQ,OAAO,YAAY;AAExC,QAAM,YAAY,MAAM,OAAO,OAAO;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,kBAAkB;AAAA,IACtB,OAAO,aAAa,GAAG,IAAI,WAAW,SAAS,CAAC;AAAA,EAClD;AAEA,SAAO,GAAG,UAAU,KAAK,UAAU,MAAM,CAAC,CAAC,IAAI;AAAA,IAC7C,KAAK,UAAU,OAAO;AAAA,EACxB,CAAC,IAAI,eAAe;AACtB;AAMA,eAAsB,kBAAkB,aAAiC;AACvE,MAAI;AACF,UAAM,QAAQ,eAAgB,MAAM,gBAAgB;AACpD,UAAM,MAAM,MAAM,SAAS,KAAK;AAEhC,UAAM,WAAW,MAAM,MAAM,uCAAuC;AAAA,MAClE,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,oCAAoC;AAAA,MAC/D,MAAM,IAAI,gBAAgB;AAAA,QACxB,YAAY;AAAA,QACZ,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAED,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI,MAAM,yBAAyB,SAAS,UAAU,EAAE;AAAA,IAChE;AAEA,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,WAAO,KAAK;AAAA,EACd,SAAS,OAAO;AACd,UAAM;AAAA,EACR;AACF;;;AN/HO,SAASC,cACd,UAAwC,CAAC,GACnB;AACtB,SAAO,aAAqB;AAAA,IAC1B,GAAG;AAAA,IACH,SAAS,aAAa;AAAA,MACpB,eAAe,UAAU,MAAM;AAAA,QAC7B,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,GAAI,UAAM,gCAAQ,QAAQ,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;AAKO,IAAM,SAASA,cAAa;", "names": ["createVertex", "import_provider_utils", "import_provider_utils", "import_provider_utils", "import_zod", "import_provider_utils", "import_zod", "import_provider_utils", "createVertex"]}