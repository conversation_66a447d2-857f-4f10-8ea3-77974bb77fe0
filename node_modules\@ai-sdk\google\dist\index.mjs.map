{"version": 3, "sources": ["../src/google-provider.ts", "../src/google-generative-ai-language-model.ts", "../src/convert-json-schema-to-openapi-schema.ts", "../src/convert-to-google-generative-ai-messages.ts", "../src/get-model-path.ts", "../src/google-error.ts", "../src/google-prepare-tools.ts", "../src/map-google-generative-ai-finish-reason.ts", "../src/google-generative-ai-embedding-model.ts", "../src/google-supported-file-url.ts"], "sourcesContent": ["import {\n  FetchFunction,\n  generateId,\n  loadApi<PERSON><PERSON>,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport { GoogleGenerativeAILanguageModel } from './google-generative-ai-language-model';\nimport {\n  GoogleGenerativeAIModelId,\n  GoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { GoogleGenerativeAIEmbeddingModel } from './google-generative-ai-embedding-model';\nimport {\n  GoogleGenerativeAIEmbeddingModelId,\n  GoogleGenerativeAIEmbeddingSettings,\n} from './google-generative-ai-embedding-settings';\nimport {\n  EmbeddingModelV1,\n  LanguageModelV1,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport { isSupportedFileUrl } from './google-supported-file-url';\n\nexport interface GoogleGenerativeAIProvider extends ProviderV1 {\n  (\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  languageModel(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  chat(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  /**\n   * @deprecated Use `chat()` instead.\n   */\n  generativeAI(\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `textEmbeddingModel()` instead.\n   */\n  embedding(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\n@deprecated Use `textEmbeddingModel()` instead.\n */\n  textEmbedding(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  textEmbeddingModel(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings?: GoogleGenerativeAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n}\n\nexport interface GoogleGenerativeAIProviderSettings {\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://generativelanguage.googleapis.com/v1beta`.\n   */\n  baseURL?: string;\n\n  /**\nAPI key that is being send using the `x-goog-api-key` header.\nIt defaults to the `GOOGLE_GENERATIVE_AI_API_KEY` environment variable.\n   */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string | undefined>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  /**\nOptional function to generate a unique ID for each request.\n     */\n  generateId?: () => string;\n}\n\n/**\nCreate a Google Generative AI provider instance.\n */\nexport function createGoogleGenerativeAI(\n  options: GoogleGenerativeAIProviderSettings = {},\n): GoogleGenerativeAIProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ??\n    'https://generativelanguage.googleapis.com/v1beta';\n\n  const getHeaders = () => ({\n    'x-goog-api-key': loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'GOOGLE_GENERATIVE_AI_API_KEY',\n      description: 'Google Generative AI',\n    }),\n    ...options.headers,\n  });\n\n  const createChatModel = (\n    modelId: GoogleGenerativeAIModelId,\n    settings: GoogleGenerativeAISettings = {},\n  ) =>\n    new GoogleGenerativeAILanguageModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      baseURL,\n      headers: getHeaders,\n      generateId: options.generateId ?? generateId,\n      isSupportedUrl: isSupportedFileUrl,\n      fetch: options.fetch,\n    });\n\n  const createEmbeddingModel = (\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings: GoogleGenerativeAIEmbeddingSettings = {},\n  ) =>\n    new GoogleGenerativeAIEmbeddingModel(modelId, settings, {\n      provider: 'google.generative-ai',\n      baseURL,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const provider = function (\n    modelId: GoogleGenerativeAIModelId,\n    settings?: GoogleGenerativeAISettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Google Generative AI model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.generativeAI = createChatModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n\n  return provider;\n}\n\n/**\nDefault Google Generative AI provider instance.\n */\nexport const google = createGoogleGenerativeAI();\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1ProviderMetadata,\n  LanguageModelV1Source,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  ParseResult,\n  Resolvable,\n  combineHeaders,\n  createEventSourceResponseHandler,\n  createJsonResponseHandler,\n  parseProviderOptions,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { convertJSONSchemaToOpenAPISchema } from './convert-json-schema-to-openapi-schema';\nimport { convertToGoogleGenerativeAIMessages } from './convert-to-google-generative-ai-messages';\nimport { getModelPath } from './get-model-path';\nimport { googleFailedResponseHandler } from './google-error';\nimport { GoogleGenerativeAIContentPart } from './google-generative-ai-prompt';\nimport {\n  GoogleGenerativeAIModelId,\n  InternalGoogleGenerativeAISettings,\n} from './google-generative-ai-settings';\nimport { prepareTools } from './google-prepare-tools';\nimport { mapGoogleGenerativeAIFinishReason } from './map-google-generative-ai-finish-reason';\n\ntype GoogleGenerativeAIConfig = {\n  provider: string;\n  baseURL: string;\n  headers: Resolvable<Record<string, string | undefined>>;\n  fetch?: FetchFunction;\n  generateId: () => string;\n  isSupportedUrl: (url: URL) => boolean;\n};\n\nexport class GoogleGenerativeAILanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1';\n  readonly defaultObjectGenerationMode = 'json';\n  readonly supportsImageUrls = false;\n\n  get supportsStructuredOutputs() {\n    return this.settings.structuredOutputs ?? true;\n  }\n\n  readonly modelId: GoogleGenerativeAIModelId;\n  readonly settings: InternalGoogleGenerativeAISettings;\n\n  private readonly config: GoogleGenerativeAIConfig;\n\n  constructor(\n    modelId: GoogleGenerativeAIModelId,\n    settings: InternalGoogleGenerativeAISettings,\n    config: GoogleGenerativeAIConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  private async getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type;\n\n    const warnings: LanguageModelV1CallWarning[] = [];\n\n    const googleOptions = parseProviderOptions({\n      provider: 'google',\n      providerOptions: providerMetadata,\n      schema: googleGenerativeAIProviderOptionsSchema,\n    });\n\n    // Add warning if includeThoughts is used with a non-Vertex Google provider\n    if (\n      googleOptions?.thinkingConfig?.includeThoughts === true &&\n      !this.config.provider.startsWith('google.vertex.')\n    ) {\n      warnings.push({\n        type: 'other',\n        message:\n          \"The 'includeThoughts' option is only supported with the Google Vertex provider \" +\n          'and might not be supported or could behave unexpectedly with the current Google provider ' +\n          `(${this.config.provider}).`,\n      });\n    }\n\n    const generationConfig = {\n      // standardized settings:\n      maxOutputTokens: maxTokens,\n      temperature,\n      topK,\n      topP,\n      frequencyPenalty,\n      presencePenalty,\n      stopSequences,\n      seed,\n\n      // response format:\n      responseMimeType:\n        responseFormat?.type === 'json' ? 'application/json' : undefined,\n      responseSchema:\n        responseFormat?.type === 'json' &&\n        responseFormat.schema != null &&\n        // Google GenAI does not support all OpenAPI Schema features,\n        // so this is needed as an escape hatch:\n        this.supportsStructuredOutputs\n          ? convertJSONSchemaToOpenAPISchema(responseFormat.schema)\n          : undefined,\n      ...(this.settings.audioTimestamp && {\n        audioTimestamp: this.settings.audioTimestamp,\n      }),\n\n      // provider options:\n      responseModalities: googleOptions?.responseModalities,\n      thinkingConfig: googleOptions?.thinkingConfig,\n    };\n\n    const { contents, systemInstruction } =\n      convertToGoogleGenerativeAIMessages(prompt);\n\n    switch (type) {\n      case 'regular': {\n        const { tools, toolConfig, toolWarnings } = prepareTools(\n          mode,\n          this.settings.useSearchGrounding ?? false,\n          this.settings.dynamicRetrievalConfig,\n          this.modelId,\n        );\n\n        return {\n          args: {\n            generationConfig,\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            tools,\n            toolConfig,\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings: [...warnings, ...toolWarnings],\n        };\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            generationConfig: {\n              ...generationConfig,\n              responseMimeType: 'application/json',\n              responseSchema:\n                mode.schema != null &&\n                // Google GenAI does not support all OpenAPI Schema features,\n                // so this is needed as an escape hatch:\n                this.supportsStructuredOutputs\n                  ? convertJSONSchemaToOpenAPISchema(mode.schema)\n                  : undefined,\n            },\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      case 'object-tool': {\n        return {\n          args: {\n            generationConfig,\n            contents,\n            tools: {\n              functionDeclarations: [\n                {\n                  name: mode.tool.name,\n                  description: mode.tool.description ?? '',\n                  parameters: convertJSONSchemaToOpenAPISchema(\n                    mode.tool.parameters,\n                  ),\n                },\n              ],\n            },\n            toolConfig: { functionCallingConfig: { mode: 'ANY' } },\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent,\n          },\n          warnings,\n        };\n      }\n\n      default: {\n        const _exhaustiveCheck: never = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n\n  supportsUrl(url: URL): boolean {\n    return this.config.isSupportedUrl(url);\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = await this.getArgs(options);\n    const body = JSON.stringify(args);\n\n    const mergedHeaders = combineHeaders(\n      await resolve(this.config.headers),\n      options.headers,\n    );\n\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse,\n    } = await postJsonToApi({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId,\n      )}:generateContent`,\n      headers: mergedHeaders,\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(responseSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { contents: rawPrompt, ...rawSettings } = args;\n    const candidate = response.candidates[0];\n\n    const parts =\n      candidate.content == null ||\n      typeof candidate.content !== 'object' ||\n      !('parts' in candidate.content)\n        ? []\n        : candidate.content.parts;\n\n    const toolCalls = getToolCallsFromParts({\n      parts: parts, // Use candidateParts\n      generateId: this.config.generateId,\n    });\n\n    const usageMetadata = response.usageMetadata;\n\n    return {\n      text: getTextFromParts(parts),\n      reasoning: getReasoningDetailsFromParts(parts),\n      files: getInlineDataParts(parts)?.map(part => ({\n        data: part.inlineData.data,\n        mimeType: part.inlineData.mimeType,\n      })),\n      toolCalls,\n      finishReason: mapGoogleGenerativeAIFinishReason({\n        finishReason: candidate.finishReason,\n        hasToolCalls: toolCalls != null && toolCalls.length > 0,\n      }),\n      usage: {\n        promptTokens: usageMetadata?.promptTokenCount ?? NaN,\n        completionTokens: usageMetadata?.candidatesTokenCount ?? NaN,\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      warnings,\n      providerMetadata: {\n        google: {\n          groundingMetadata: candidate.groundingMetadata ?? null,\n          safetyRatings: candidate.safetyRatings ?? null,\n        },\n      },\n      sources: extractSources({\n        groundingMetadata: candidate.groundingMetadata,\n        generateId: this.config.generateId,\n      }),\n      request: { body },\n    };\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    const { args, warnings } = await this.getArgs(options);\n\n    const body = JSON.stringify(args);\n    const headers = combineHeaders(\n      await resolve(this.config.headers),\n      options.headers,\n    );\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId,\n      )}:streamGenerateContent?alt=sse`,\n      headers,\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createEventSourceResponseHandler(chunkSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    const { contents: rawPrompt, ...rawSettings } = args;\n\n    let finishReason: LanguageModelV1FinishReason = 'unknown';\n    let usage: { promptTokens: number; completionTokens: number } = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN,\n    };\n    let providerMetadata: LanguageModelV1ProviderMetadata | undefined =\n      undefined;\n\n    const generateId = this.config.generateId;\n    let hasToolCalls = false;\n\n    return {\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof chunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ type: 'error', error: chunk.error });\n              return;\n            }\n\n            const value = chunk.value;\n\n            const usageMetadata = value.usageMetadata;\n\n            if (usageMetadata != null) {\n              usage = {\n                promptTokens: usageMetadata.promptTokenCount ?? NaN,\n                completionTokens: usageMetadata.candidatesTokenCount ?? NaN,\n              };\n            }\n\n            const candidate = value.candidates?.[0];\n\n            // sometimes the API returns an empty candidates array\n            if (candidate == null) {\n              return;\n            }\n\n            const content = candidate.content;\n\n            // Process tool call's parts before determining finishReason to ensure hasToolCalls is properly set\n            if (content != null) {\n              const deltaText = getTextFromParts(content.parts);\n              if (deltaText != null) {\n                controller.enqueue({\n                  type: 'text-delta',\n                  textDelta: deltaText,\n                });\n              }\n\n              const reasoningDeltaText = getReasoningDetailsFromParts(\n                content.parts,\n              );\n              if (reasoningDeltaText != null) {\n                for (const part of reasoningDeltaText) {\n                  controller.enqueue({\n                    type: 'reasoning',\n                    textDelta: part.text,\n                  });\n                }\n              }\n\n              const inlineDataParts = getInlineDataParts(content.parts);\n              if (inlineDataParts != null) {\n                for (const part of inlineDataParts) {\n                  controller.enqueue({\n                    type: 'file',\n                    mimeType: part.inlineData.mimeType,\n                    data: part.inlineData.data,\n                  });\n                }\n              }\n\n              const toolCallDeltas = getToolCallsFromParts({\n                parts: content.parts,\n                generateId,\n              });\n\n              if (toolCallDeltas != null) {\n                for (const toolCall of toolCallDeltas) {\n                  controller.enqueue({\n                    type: 'tool-call-delta',\n                    toolCallType: 'function',\n                    toolCallId: toolCall.toolCallId,\n                    toolName: toolCall.toolName,\n                    argsTextDelta: toolCall.args,\n                  });\n\n                  controller.enqueue({\n                    type: 'tool-call',\n                    toolCallType: 'function',\n                    toolCallId: toolCall.toolCallId,\n                    toolName: toolCall.toolName,\n                    args: toolCall.args,\n                  });\n\n                  hasToolCalls = true;\n                }\n              }\n            }\n\n            if (candidate.finishReason != null) {\n              finishReason = mapGoogleGenerativeAIFinishReason({\n                finishReason: candidate.finishReason,\n                hasToolCalls,\n              });\n\n              const sources =\n                extractSources({\n                  groundingMetadata: candidate.groundingMetadata,\n                  generateId,\n                }) ?? [];\n\n              for (const source of sources) {\n                controller.enqueue({ type: 'source', source });\n              }\n\n              providerMetadata = {\n                google: {\n                  groundingMetadata: candidate.groundingMetadata ?? null,\n                  safetyRatings: candidate.safetyRatings ?? null,\n                },\n              };\n            }\n          },\n\n          flush(controller) {\n            controller.enqueue({\n              type: 'finish',\n              finishReason,\n              usage,\n              providerMetadata,\n            });\n          },\n        }),\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body },\n    };\n  }\n}\n\nfunction getToolCallsFromParts({\n  parts,\n  generateId,\n}: {\n  parts: z.infer<typeof contentSchema>['parts'];\n  generateId: () => string;\n}) {\n  const functionCallParts = parts?.filter(\n    part => 'functionCall' in part,\n  ) as Array<\n    GoogleGenerativeAIContentPart & {\n      functionCall: { name: string; args: unknown };\n    }\n  >;\n\n  return functionCallParts == null || functionCallParts.length === 0\n    ? undefined\n    : functionCallParts.map(part => ({\n        toolCallType: 'function' as const,\n        toolCallId: generateId(),\n        toolName: part.functionCall.name,\n        args: JSON.stringify(part.functionCall.args),\n      }));\n}\n\nfunction getTextFromParts(parts: z.infer<typeof contentSchema>['parts']) {\n  const textParts = parts?.filter(\n    part => 'text' in part && (part as any).thought !== true, // Exclude thought parts\n  ) as Array<GoogleGenerativeAIContentPart & { text: string }>;\n\n  return textParts == null || textParts.length === 0\n    ? undefined\n    : textParts.map(part => part.text).join('');\n}\n\nfunction getReasoningDetailsFromParts(\n  parts: z.infer<typeof contentSchema>['parts'],\n): Array<{ type: 'text'; text: string }> | undefined {\n  const reasoningParts = parts?.filter(\n    part =>\n      'text' in part && (part as any).thought === true && part.text != null,\n  ) as Array<\n    GoogleGenerativeAIContentPart & { text: string; thought?: boolean }\n  >;\n\n  return reasoningParts == null || reasoningParts.length === 0\n    ? undefined\n    : reasoningParts.map(part => ({ type: 'text', text: part.text }));\n}\n\nfunction getInlineDataParts(parts: z.infer<typeof contentSchema>['parts']) {\n  return parts?.filter(\n    (\n      part,\n    ): part is {\n      inlineData: { mimeType: string; data: string };\n    } => 'inlineData' in part,\n  );\n}\n\nfunction extractSources({\n  groundingMetadata,\n  generateId,\n}: {\n  groundingMetadata: z.infer<typeof groundingMetadataSchema> | undefined | null;\n  generateId: () => string;\n}): undefined | LanguageModelV1Source[] {\n  return groundingMetadata?.groundingChunks\n    ?.filter(\n      (\n        chunk,\n      ): chunk is z.infer<typeof groundingChunkSchema> & {\n        web: { uri: string; title?: string };\n      } => chunk.web != null,\n    )\n    .map(chunk => ({\n      sourceType: 'url',\n      id: generateId(),\n      url: chunk.web.uri,\n      title: chunk.web.title,\n    }));\n}\n\nconst contentSchema = z.object({\n  parts: z\n    .array(\n      z.union([\n        // note: order matters since text can be fully empty\n        z.object({\n          functionCall: z.object({\n            name: z.string(),\n            args: z.unknown(),\n          }),\n        }),\n        z.object({\n          inlineData: z.object({\n            mimeType: z.string(),\n            data: z.string(),\n          }),\n        }),\n        z.object({\n          text: z.string().nullish(),\n          thought: z.boolean().nullish(),\n        }),\n      ]),\n    )\n    .nullish(),\n});\n\n// https://ai.google.dev/gemini-api/docs/grounding\n// https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/ground-gemini#ground-to-search\nconst groundingChunkSchema = z.object({\n  web: z.object({ uri: z.string(), title: z.string() }).nullish(),\n  retrievedContext: z.object({ uri: z.string(), title: z.string() }).nullish(),\n});\n\nexport const groundingMetadataSchema = z.object({\n  webSearchQueries: z.array(z.string()).nullish(),\n  retrievalQueries: z.array(z.string()).nullish(),\n  searchEntryPoint: z.object({ renderedContent: z.string() }).nullish(),\n  groundingChunks: z.array(groundingChunkSchema).nullish(),\n  groundingSupports: z\n    .array(\n      z.object({\n        segment: z.object({\n          startIndex: z.number().nullish(),\n          endIndex: z.number().nullish(),\n          text: z.string().nullish(),\n        }),\n        segment_text: z.string().nullish(),\n        groundingChunkIndices: z.array(z.number()).nullish(),\n        supportChunkIndices: z.array(z.number()).nullish(),\n        confidenceScores: z.array(z.number()).nullish(),\n        confidenceScore: z.array(z.number()).nullish(),\n      }),\n    )\n    .nullish(),\n  retrievalMetadata: z\n    .union([\n      z.object({\n        webDynamicRetrievalScore: z.number(),\n      }),\n      z.object({}),\n    ])\n    .nullish(),\n});\n\n// https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/configure-safety-filters\nexport const safetyRatingSchema = z.object({\n  category: z.string().nullish(),\n  probability: z.string().nullish(),\n  probabilityScore: z.number().nullish(),\n  severity: z.string().nullish(),\n  severityScore: z.number().nullish(),\n  blocked: z.boolean().nullish(),\n});\n\nconst responseSchema = z.object({\n  candidates: z.array(\n    z.object({\n      content: contentSchema.nullish().or(z.object({}).strict()),\n      finishReason: z.string().nullish(),\n      safetyRatings: z.array(safetyRatingSchema).nullish(),\n      groundingMetadata: groundingMetadataSchema.nullish(),\n    }),\n  ),\n  usageMetadata: z\n    .object({\n      promptTokenCount: z.number().nullish(),\n      candidatesTokenCount: z.number().nullish(),\n      totalTokenCount: z.number().nullish(),\n    })\n    .nullish(),\n});\n\n// limited version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst chunkSchema = z.object({\n  candidates: z\n    .array(\n      z.object({\n        content: contentSchema.nullish(),\n        finishReason: z.string().nullish(),\n        safetyRatings: z.array(safetyRatingSchema).nullish(),\n        groundingMetadata: groundingMetadataSchema.nullish(),\n      }),\n    )\n    .nullish(),\n  usageMetadata: z\n    .object({\n      promptTokenCount: z.number().nullish(),\n      candidatesTokenCount: z.number().nullish(),\n      totalTokenCount: z.number().nullish(),\n    })\n    .nullish(),\n});\n\nconst googleGenerativeAIProviderOptionsSchema = z.object({\n  responseModalities: z.array(z.enum(['TEXT', 'IMAGE'])).nullish(),\n  thinkingConfig: z\n    .object({\n      thinkingBudget: z.number().nullish(),\n      includeThoughts: z.boolean().nullish(),\n    })\n    .nullish(),\n});\nexport type GoogleGenerativeAIProviderOptions = z.infer<\n  typeof googleGenerativeAIProviderOptionsSchema\n>;\n", "import { JSONSchema7Definition } from '@ai-sdk/provider';\n\n/**\n * Converts JSON Schema 7 to OpenAPI Schema 3.0\n */\nexport function convertJSONSchemaToOpenAPISchema(\n  jsonSchema: JSONSchema7Definition,\n): unknown {\n  // parameters need to be undefined if they are empty objects:\n  if (isEmptyObjectSchema(jsonSchema)) {\n    return undefined;\n  }\n\n  if (typeof jsonSchema === 'boolean') {\n    return { type: 'boolean', properties: {} };\n  }\n\n  const {\n    type,\n    description,\n    required,\n    properties,\n    items,\n    allOf,\n    anyOf,\n    oneOf,\n    format,\n    const: constValue,\n    minLength,\n    enum: enumValues,\n  } = jsonSchema;\n\n  const result: Record<string, unknown> = {};\n\n  if (description) result.description = description;\n  if (required) result.required = required;\n  if (format) result.format = format;\n\n  if (constValue !== undefined) {\n    result.enum = [constValue];\n  }\n\n  // Handle type\n  if (type) {\n    if (Array.isArray(type)) {\n      if (type.includes('null')) {\n        result.type = type.filter(t => t !== 'null')[0];\n        result.nullable = true;\n      } else {\n        result.type = type;\n      }\n    } else if (type === 'null') {\n      result.type = 'null';\n    } else {\n      result.type = type;\n    }\n  }\n\n  // Handle enum\n  if (enumValues !== undefined) {\n    result.enum = enumValues;\n  }\n\n  if (properties != null) {\n    result.properties = Object.entries(properties).reduce(\n      (acc, [key, value]) => {\n        acc[key] = convertJSONSchemaToOpenAPISchema(value);\n        return acc;\n      },\n      {} as Record<string, unknown>,\n    );\n  }\n\n  if (items) {\n    result.items = Array.isArray(items)\n      ? items.map(convertJSONSchemaToOpenAPISchema)\n      : convertJSONSchemaToOpenAPISchema(items);\n  }\n\n  if (allOf) {\n    result.allOf = allOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n  if (anyOf) {\n    // Handle cases where anyOf includes a null type\n    if (\n      anyOf.some(\n        schema => typeof schema === 'object' && schema?.type === 'null',\n      )\n    ) {\n      const nonNullSchemas = anyOf.filter(\n        schema => !(typeof schema === 'object' && schema?.type === 'null'),\n      );\n\n      if (nonNullSchemas.length === 1) {\n        // If there's only one non-null schema, convert it and make it nullable\n        const converted = convertJSONSchemaToOpenAPISchema(nonNullSchemas[0]);\n        if (typeof converted === 'object') {\n          result.nullable = true;\n          Object.assign(result, converted);\n        }\n      } else {\n        // If there are multiple non-null schemas, keep them in anyOf\n        result.anyOf = nonNullSchemas.map(convertJSONSchemaToOpenAPISchema);\n        result.nullable = true;\n      }\n    } else {\n      result.anyOf = anyOf.map(convertJSONSchemaToOpenAPISchema);\n    }\n  }\n  if (oneOf) {\n    result.oneOf = oneOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n\n  if (minLength !== undefined) {\n    result.minLength = minLength;\n  }\n\n  return result;\n}\n\nfunction isEmptyObjectSchema(jsonSchema: JSONSchema7Definition): boolean {\n  return (\n    jsonSchema != null &&\n    typeof jsonSchema === 'object' &&\n    jsonSchema.type === 'object' &&\n    (jsonSchema.properties == null ||\n      Object.keys(jsonSchema.properties).length === 0)\n  );\n}\n", "import {\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils';\nimport {\n  GoogleGenerativeAIContent,\n  GoogleGenerativeAIContentPart,\n  GoogleGenerativeAIPrompt,\n} from './google-generative-ai-prompt';\n\nexport function convertToGoogleGenerativeAIMessages(\n  prompt: LanguageModelV1Prompt,\n): GoogleGenerativeAIPrompt {\n  const systemInstructionParts: Array<{ text: string }> = [];\n  const contents: Array<GoogleGenerativeAIContent> = [];\n  let systemMessagesAllowed = true;\n\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case 'system': {\n        if (!systemMessagesAllowed) {\n          throw new UnsupportedFunctionalityError({\n            functionality:\n              'system messages are only supported at the beginning of the conversation',\n          });\n        }\n\n        systemInstructionParts.push({ text: content });\n        break;\n      }\n\n      case 'user': {\n        systemMessagesAllowed = false;\n\n        const parts: GoogleGenerativeAIContentPart[] = [];\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              parts.push({ text: part.text });\n              break;\n            }\n\n            case 'image': {\n              parts.push(\n                part.image instanceof URL\n                  ? {\n                      fileData: {\n                        mimeType: part.mimeType ?? 'image/jpeg',\n                        fileUri: part.image.toString(),\n                      },\n                    }\n                  : {\n                      inlineData: {\n                        mimeType: part.mimeType ?? 'image/jpeg',\n                        data: convertUint8ArrayToBase64(part.image),\n                      },\n                    },\n              );\n\n              break;\n            }\n\n            case 'file': {\n              parts.push(\n                part.data instanceof URL\n                  ? {\n                      fileData: {\n                        mimeType: part.mimeType,\n                        fileUri: part.data.toString(),\n                      },\n                    }\n                  : {\n                      inlineData: {\n                        mimeType: part.mimeType,\n                        data: part.data,\n                      },\n                    },\n              );\n\n              break;\n            }\n          }\n        }\n\n        contents.push({ role: 'user', parts });\n        break;\n      }\n\n      case 'assistant': {\n        systemMessagesAllowed = false;\n\n        contents.push({\n          role: 'model',\n          parts: content\n            .map(part => {\n              switch (part.type) {\n                case 'text': {\n                  return part.text.length === 0\n                    ? undefined\n                    : { text: part.text };\n                }\n\n                case 'file': {\n                  if (part.mimeType !== 'image/png') {\n                    throw new UnsupportedFunctionalityError({\n                      functionality:\n                        'Only PNG images are supported in assistant messages',\n                    });\n                  }\n\n                  if (part.data instanceof URL) {\n                    throw new UnsupportedFunctionalityError({\n                      functionality:\n                        'File data URLs in assistant messages are not supported',\n                    });\n                  }\n\n                  return {\n                    inlineData: {\n                      mimeType: part.mimeType,\n                      data: part.data,\n                    },\n                  };\n                }\n\n                case 'tool-call': {\n                  return {\n                    functionCall: {\n                      name: part.toolName,\n                      args: part.args,\n                    },\n                  };\n                }\n              }\n            })\n            .filter(part => part !== undefined),\n        });\n        break;\n      }\n\n      case 'tool': {\n        systemMessagesAllowed = false;\n\n        contents.push({\n          role: 'user',\n          parts: content.map(part => ({\n            functionResponse: {\n              name: part.toolName,\n              response: {\n                name: part.toolName,\n                content: part.result,\n              },\n            },\n          })),\n        });\n        break;\n      }\n    }\n  }\n\n  return {\n    systemInstruction:\n      systemInstructionParts.length > 0\n        ? { parts: systemInstructionParts }\n        : undefined,\n    contents,\n  };\n}\n", "export function getModelPath(modelId: string): string {\n  return modelId.includes('/') ? modelId : `models/${modelId}`;\n}\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\n\nconst googleErrorDataSchema = z.object({\n  error: z.object({\n    code: z.number().nullable(),\n    message: z.string(),\n    status: z.string(),\n  }),\n});\n\nexport type GoogleErrorData = z.infer<typeof googleErrorDataSchema>;\n\nexport const googleFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: googleErrorDataSchema,\n  errorToMessage: data => data.error.message,\n});\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\nimport { convertJSONSchemaToOpenAPISchema } from './convert-json-schema-to-openapi-schema';\nimport {\n  DynamicRetrievalConfig,\n  GoogleGenerativeAIModelId,\n} from './google-generative-ai-settings';\n\nexport function prepareTools(\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular';\n  },\n  useSearchGrounding: boolean,\n  dynamicRetrievalConfig: DynamicRetrievalConfig | undefined,\n  modelId: GoogleGenerativeAIModelId,\n): {\n  tools:\n    | undefined\n    | {\n        functionDeclarations: Array<{\n          name: string;\n          description: string | undefined;\n          parameters: unknown;\n        }>;\n      }\n    | {\n        googleSearchRetrieval:\n          | Record<string, never>\n          | { dynamicRetrievalConfig: DynamicRetrievalConfig };\n      }\n    | { googleSearch: Record<string, never> };\n  toolConfig:\n    | undefined\n    | {\n        functionCallingConfig: {\n          mode: 'AUTO' | 'NONE' | 'ANY';\n          allowedFunctionNames?: string[];\n        };\n      };\n  toolWarnings: LanguageModelV1CallWarning[];\n} {\n  const tools = mode.tools?.length ? mode.tools : undefined;\n  const toolWarnings: LanguageModelV1CallWarning[] = [];\n\n  const isGemini2 = modelId.includes('gemini-2');\n  const supportsDynamicRetrieval =\n    modelId.includes('gemini-1.5-flash') && !modelId.includes('-8b');\n\n  if (useSearchGrounding) {\n    return {\n      tools: isGemini2\n        ? { googleSearch: {} }\n        : {\n            googleSearchRetrieval:\n              !supportsDynamicRetrieval || !dynamicRetrievalConfig\n                ? {}\n                : { dynamicRetrievalConfig },\n          },\n      toolConfig: undefined,\n      toolWarnings,\n    };\n  }\n\n  if (tools == null) {\n    return { tools: undefined, toolConfig: undefined, toolWarnings };\n  }\n\n  const functionDeclarations = [];\n  for (const tool of tools) {\n    if (tool.type === 'provider-defined') {\n      toolWarnings.push({ type: 'unsupported-tool', tool });\n    } else {\n      functionDeclarations.push({\n        name: tool.name,\n        description: tool.description ?? '',\n        parameters: convertJSONSchemaToOpenAPISchema(tool.parameters),\n      });\n    }\n  }\n\n  const toolChoice = mode.toolChoice;\n\n  if (toolChoice == null) {\n    return {\n      tools: { functionDeclarations },\n      toolConfig: undefined,\n      toolWarnings,\n    };\n  }\n\n  const type = toolChoice.type;\n\n  switch (type) {\n    case 'auto':\n      return {\n        tools: { functionDeclarations },\n        toolConfig: { functionCallingConfig: { mode: 'AUTO' } },\n        toolWarnings,\n      };\n    case 'none':\n      return {\n        tools: { functionDeclarations },\n        toolConfig: { functionCallingConfig: { mode: 'NONE' } },\n        toolWarnings,\n      };\n    case 'required':\n      return {\n        tools: { functionDeclarations },\n        toolConfig: { functionCallingConfig: { mode: 'ANY' } },\n        toolWarnings,\n      };\n    case 'tool':\n      return {\n        tools: { functionDeclarations },\n        toolConfig: {\n          functionCallingConfig: {\n            mode: 'ANY',\n            allowedFunctionNames: [toolChoice.toolName],\n          },\n        },\n        toolWarnings,\n      };\n    default: {\n      const _exhaustiveCheck: never = type;\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      });\n    }\n  }\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\n\nexport function mapGoogleGenerativeAIFinishReason({\n  finishReason,\n  hasToolCalls,\n}: {\n  finishReason: string | null | undefined;\n  hasToolCalls: boolean;\n}): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'STOP':\n      return hasToolCalls ? 'tool-calls' : 'stop';\n    case 'MAX_TOKENS':\n      return 'length';\n    case 'IMAGE_SAFETY':\n    case 'RECITATION':\n    case 'SAFETY':\n    case 'BLOCKLIST':\n    case 'PROHIBITED_CONTENT':\n    case 'SPII':\n      return 'content-filter';\n    case 'FINISH_REASON_UNSPECIFIED':\n    case 'OTHER':\n      return 'other';\n    case 'MALFORMED_FUNCTION_CALL':\n      return 'error';\n    default:\n      return 'unknown';\n  }\n}\n", "import {\n  EmbeddingModelV1,\n  TooManyEmbeddingValuesForCallError,\n} from '@ai-sdk/provider';\nimport {\n  combineHeaders,\n  createJsonResponseHandler,\n  FetchFunction,\n  postJsonToApi,\n  resolve,\n} from '@ai-sdk/provider-utils';\nimport { z } from 'zod';\nimport { googleFailedResponseHandler } from './google-error';\nimport {\n  GoogleGenerativeAIEmbeddingModelId,\n  GoogleGenerativeAIEmbeddingSettings,\n} from './google-generative-ai-embedding-settings';\n\ntype GoogleGenerativeAIEmbeddingConfig = {\n  provider: string;\n  baseURL: string;\n  headers: () => Record<string, string | undefined>;\n  fetch?: FetchFunction;\n};\n\nexport class GoogleGenerativeAIEmbeddingModel\n  implements EmbeddingModelV1<string>\n{\n  readonly specificationVersion = 'v1';\n  readonly modelId: GoogleGenerativeAIEmbeddingModelId;\n\n  private readonly config: GoogleGenerativeAIEmbeddingConfig;\n  private readonly settings: GoogleGenerativeAIEmbeddingSettings;\n\n  get provider(): string {\n    return this.config.provider;\n  }\n\n  get maxEmbeddingsPerCall(): number {\n    return 2048;\n  }\n\n  get supportsParallelCalls(): boolean {\n    return true;\n  }\n\n  constructor(\n    modelId: GoogleGenerativeAIEmbeddingModelId,\n    settings: GoogleGenerativeAIEmbeddingSettings,\n    config: GoogleGenerativeAIEmbeddingConfig,\n  ) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n\n  async doEmbed({\n    values,\n    headers,\n    abortSignal,\n  }: Parameters<EmbeddingModelV1<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>\n  > {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values,\n      });\n    }\n\n    const mergedHeaders = combineHeaders(\n      await resolve(this.config.headers),\n      headers,\n    );\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      url: `${this.config.baseURL}/models/${this.modelId}:batchEmbedContents`,\n      headers: mergedHeaders,\n      body: {\n        requests: values.map(value => ({\n          model: `models/${this.modelId}`,\n          content: { role: 'user', parts: [{ text: value }] },\n          outputDimensionality: this.settings.outputDimensionality,\n          taskType: this.settings.taskType,\n        })),\n      },\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: createJsonResponseHandler(\n        googleGenerativeAITextEmbeddingResponseSchema,\n      ),\n      abortSignal,\n      fetch: this.config.fetch,\n    });\n\n    return {\n      embeddings: response.embeddings.map(item => item.values),\n      usage: undefined,\n      rawResponse: { headers: responseHeaders },\n    };\n  }\n}\n\n// minimal version of the schema, focussed on what is needed for the implementation\n// this approach limits breakages when the API changes and increases efficiency\nconst googleGenerativeAITextEmbeddingResponseSchema = z.object({\n  embeddings: z.array(z.object({ values: z.array(z.number()) })),\n});\n", "export function isSupportedFileUrl(url: URL): boolean {\n  return url\n    .toString()\n    .startsWith('https://generativelanguage.googleapis.com/v1beta/files/');\n}\n"], "mappings": ";AAAA;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,OACK;;;ACGP;AAAA,EAIE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,KAAAA,UAAS;;;ACdX,SAAS,iCACd,YACS;AAET,MAAI,oBAAoB,UAAU,GAAG;AACnC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,eAAe,WAAW;AACnC,WAAO,EAAE,MAAM,WAAW,YAAY,CAAC,EAAE;AAAA,EAC3C;AAEA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA,MAAM;AAAA,EACR,IAAI;AAEJ,QAAM,SAAkC,CAAC;AAEzC,MAAI;AAAa,WAAO,cAAc;AACtC,MAAI;AAAU,WAAO,WAAW;AAChC,MAAI;AAAQ,WAAO,SAAS;AAE5B,MAAI,eAAe,QAAW;AAC5B,WAAO,OAAO,CAAC,UAAU;AAAA,EAC3B;AAGA,MAAI,MAAM;AACR,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,UAAI,KAAK,SAAS,MAAM,GAAG;AACzB,eAAO,OAAO,KAAK,OAAO,OAAK,MAAM,MAAM,EAAE,CAAC;AAC9C,eAAO,WAAW;AAAA,MACpB,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAAA,IACF,WAAW,SAAS,QAAQ;AAC1B,aAAO,OAAO;AAAA,IAChB,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAGA,MAAI,eAAe,QAAW;AAC5B,WAAO,OAAO;AAAA,EAChB;AAEA,MAAI,cAAc,MAAM;AACtB,WAAO,aAAa,OAAO,QAAQ,UAAU,EAAE;AAAA,MAC7C,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrB,YAAI,GAAG,IAAI,iCAAiC,KAAK;AACjD,eAAO;AAAA,MACT;AAAA,MACA,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,QAAQ,KAAK,IAC9B,MAAM,IAAI,gCAAgC,IAC1C,iCAAiC,KAAK;AAAA,EAC5C;AAEA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;AAAA,EAC3D;AACA,MAAI,OAAO;AAET,QACE,MAAM;AAAA,MACJ,YAAU,OAAO,WAAW,aAAY,iCAAQ,UAAS;AAAA,IAC3D,GACA;AACA,YAAM,iBAAiB,MAAM;AAAA,QAC3B,YAAU,EAAE,OAAO,WAAW,aAAY,iCAAQ,UAAS;AAAA,MAC7D;AAEA,UAAI,eAAe,WAAW,GAAG;AAE/B,cAAM,YAAY,iCAAiC,eAAe,CAAC,CAAC;AACpE,YAAI,OAAO,cAAc,UAAU;AACjC,iBAAO,WAAW;AAClB,iBAAO,OAAO,QAAQ,SAAS;AAAA,QACjC;AAAA,MACF,OAAO;AAEL,eAAO,QAAQ,eAAe,IAAI,gCAAgC;AAClE,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,MAAM,IAAI,gCAAgC;AAAA,IAC3D;AAAA,EACF;AACA,MAAI,OAAO;AACT,WAAO,QAAQ,MAAM,IAAI,gCAAgC;AAAA,EAC3D;AAEA,MAAI,cAAc,QAAW;AAC3B,WAAO,YAAY;AAAA,EACrB;AAEA,SAAO;AACT;AAEA,SAAS,oBAAoB,YAA4C;AACvE,SACE,cAAc,QACd,OAAO,eAAe,YACtB,WAAW,SAAS,aACnB,WAAW,cAAc,QACxB,OAAO,KAAK,WAAW,UAAU,EAAE,WAAW;AAEpD;;;AChIA;AAAA,EAEE;AAAA,OACK;AACP,SAAS,iCAAiC;AAOnC,SAAS,oCACd,QAC0B;AAb5B;AAcE,QAAM,yBAAkD,CAAC;AACzD,QAAM,WAA6C,CAAC;AACpD,MAAI,wBAAwB;AAE5B,aAAW,EAAE,MAAM,QAAQ,KAAK,QAAQ;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,YAAI,CAAC,uBAAuB;AAC1B,gBAAM,IAAI,8BAA8B;AAAA,YACtC,eACE;AAAA,UACJ,CAAC;AAAA,QACH;AAEA,+BAAuB,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC7C;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,gCAAwB;AAExB,cAAM,QAAyC,CAAC;AAEhD,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,oBAAM,KAAK,EAAE,MAAM,KAAK,KAAK,CAAC;AAC9B;AAAA,YACF;AAAA,YAEA,KAAK,SAAS;AACZ,oBAAM;AAAA,gBACJ,KAAK,iBAAiB,MAClB;AAAA,kBACE,UAAU;AAAA,oBACR,WAAU,UAAK,aAAL,YAAiB;AAAA,oBAC3B,SAAS,KAAK,MAAM,SAAS;AAAA,kBAC/B;AAAA,gBACF,IACA;AAAA,kBACE,YAAY;AAAA,oBACV,WAAU,UAAK,aAAL,YAAiB;AAAA,oBAC3B,MAAM,0BAA0B,KAAK,KAAK;AAAA,kBAC5C;AAAA,gBACF;AAAA,cACN;AAEA;AAAA,YACF;AAAA,YAEA,KAAK,QAAQ;AACX,oBAAM;AAAA,gBACJ,KAAK,gBAAgB,MACjB;AAAA,kBACE,UAAU;AAAA,oBACR,UAAU,KAAK;AAAA,oBACf,SAAS,KAAK,KAAK,SAAS;AAAA,kBAC9B;AAAA,gBACF,IACA;AAAA,kBACE,YAAY;AAAA,oBACV,UAAU,KAAK;AAAA,oBACf,MAAM,KAAK;AAAA,kBACb;AAAA,gBACF;AAAA,cACN;AAEA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK,EAAE,MAAM,QAAQ,MAAM,CAAC;AACrC;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,gCAAwB;AAExB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO,QACJ,IAAI,UAAQ;AACX,oBAAQ,KAAK,MAAM;AAAA,cACjB,KAAK,QAAQ;AACX,uBAAO,KAAK,KAAK,WAAW,IACxB,SACA,EAAE,MAAM,KAAK,KAAK;AAAA,cACxB;AAAA,cAEA,KAAK,QAAQ;AACX,oBAAI,KAAK,aAAa,aAAa;AACjC,wBAAM,IAAI,8BAA8B;AAAA,oBACtC,eACE;AAAA,kBACJ,CAAC;AAAA,gBACH;AAEA,oBAAI,KAAK,gBAAgB,KAAK;AAC5B,wBAAM,IAAI,8BAA8B;AAAA,oBACtC,eACE;AAAA,kBACJ,CAAC;AAAA,gBACH;AAEA,uBAAO;AAAA,kBACL,YAAY;AAAA,oBACV,UAAU,KAAK;AAAA,oBACf,MAAM,KAAK;AAAA,kBACb;AAAA,gBACF;AAAA,cACF;AAAA,cAEA,KAAK,aAAa;AAChB,uBAAO;AAAA,kBACL,cAAc;AAAA,oBACZ,MAAM,KAAK;AAAA,oBACX,MAAM,KAAK;AAAA,kBACb;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC,EACA,OAAO,UAAQ,SAAS,MAAS;AAAA,QACtC,CAAC;AACD;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,gCAAwB;AAExB,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO,QAAQ,IAAI,WAAS;AAAA,YAC1B,kBAAkB;AAAA,cAChB,MAAM,KAAK;AAAA,cACX,UAAU;AAAA,gBACR,MAAM,KAAK;AAAA,gBACX,SAAS,KAAK;AAAA,cAChB;AAAA,YACF;AAAA,UACF,EAAE;AAAA,QACJ,CAAC;AACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,mBACE,uBAAuB,SAAS,IAC5B,EAAE,OAAO,uBAAuB,IAChC;AAAA,IACN;AAAA,EACF;AACF;;;ACzKO,SAAS,aAAa,SAAyB;AACpD,SAAO,QAAQ,SAAS,GAAG,IAAI,UAAU,UAAU,OAAO;AAC5D;;;ACFA,SAAS,sCAAsC;AAC/C,SAAS,SAAS;AAElB,IAAM,wBAAwB,EAAE,OAAO;AAAA,EACrC,OAAO,EAAE,OAAO;AAAA,IACd,MAAM,EAAE,OAAO,EAAE,SAAS;AAAA,IAC1B,SAAS,EAAE,OAAO;AAAA,IAClB,QAAQ,EAAE,OAAO;AAAA,EACnB,CAAC;AACH,CAAC;AAIM,IAAM,8BAA8B,+BAA+B;AAAA,EACxE,aAAa;AAAA,EACb,gBAAgB,UAAQ,KAAK,MAAM;AACrC,CAAC;;;AChBD;AAAA,EAGE,iCAAAC;AAAA,OACK;AAOA,SAAS,aACd,MAGA,oBACA,wBACA,SA0BA;AA3CF;AA4CE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAChD,QAAM,eAA6C,CAAC;AAEpD,QAAM,YAAY,QAAQ,SAAS,UAAU;AAC7C,QAAM,2BACJ,QAAQ,SAAS,kBAAkB,KAAK,CAAC,QAAQ,SAAS,KAAK;AAEjE,MAAI,oBAAoB;AACtB,WAAO;AAAA,MACL,OAAO,YACH,EAAE,cAAc,CAAC,EAAE,IACnB;AAAA,QACE,uBACE,CAAC,4BAA4B,CAAC,yBAC1B,CAAC,IACD,EAAE,uBAAuB;AAAA,MACjC;AAAA,MACJ,YAAY;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,OAAO,QAAW,YAAY,QAAW,aAAa;AAAA,EACjE;AAEA,QAAM,uBAAuB,CAAC;AAC9B,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAa,KAAK,EAAE,MAAM,oBAAoB,KAAK,CAAC;AAAA,IACtD,OAAO;AACL,2BAAqB,KAAK;AAAA,QACxB,MAAM,KAAK;AAAA,QACX,cAAa,UAAK,gBAAL,YAAoB;AAAA,QACjC,YAAY,iCAAiC,KAAK,UAAU;AAAA,MAC9D,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,aAAa,KAAK;AAExB,MAAI,cAAc,MAAM;AACtB,WAAO;AAAA,MACL,OAAO,EAAE,qBAAqB;AAAA,MAC9B,YAAY;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,qBAAqB;AAAA,QAC9B,YAAY,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;AAAA,QACtD;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,qBAAqB;AAAA,QAC9B,YAAY,EAAE,uBAAuB,EAAE,MAAM,OAAO,EAAE;AAAA,QACtD;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,qBAAqB;AAAA,QAC9B,YAAY,EAAE,uBAAuB,EAAE,MAAM,MAAM,EAAE;AAAA,QACrD;AAAA,MACF;AAAA,IACF,KAAK;AACH,aAAO;AAAA,QACL,OAAO,EAAE,qBAAqB;AAAA,QAC9B,YAAY;AAAA,UACV,uBAAuB;AAAA,YACrB,MAAM;AAAA,YACN,sBAAsB,CAAC,WAAW,QAAQ;AAAA,UAC5C;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS;AACP,YAAM,mBAA0B;AAChC,YAAM,IAAIC,+BAA8B;AAAA,QACtC,eAAe,iCAAiC,gBAAgB;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AClIO,SAAS,kCAAkC;AAAA,EAChD;AAAA,EACA;AACF,GAGgC;AAC9B,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,aAAO,eAAe,eAAe;AAAA,IACvC,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;ANYO,IAAM,kCAAN,MAAiE;AAAA,EActE,YACE,SACA,UACA,QACA;AAjBF,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AACvC,SAAS,oBAAoB;AAgB3B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EAjBA,IAAI,4BAA4B;AA9ClC;AA+CI,YAAO,UAAK,SAAS,sBAAd,YAAmC;AAAA,EAC5C;AAAA,EAiBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,MAAc,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAiD;AAlFnD;AAmFI,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,UAAM,gBAAgB,qBAAqB;AAAA,MACzC,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC;AAGD,UACE,oDAAe,mBAAf,mBAA+B,qBAAoB,QACnD,CAAC,KAAK,OAAO,SAAS,WAAW,gBAAgB,GACjD;AACA,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,SACE,4KAEI,KAAK,OAAO,QAAQ;AAAA,MAC5B,CAAC;AAAA,IACH;AAEA,UAAM,mBAAmB;AAAA;AAAA,MAEvB,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAGA,mBACE,iDAAgB,UAAS,SAAS,qBAAqB;AAAA,MACzD,iBACE,iDAAgB,UAAS,UACzB,eAAe,UAAU;AAAA;AAAA,MAGzB,KAAK,4BACD,iCAAiC,eAAe,MAAM,IACtD;AAAA,MACN,GAAI,KAAK,SAAS,kBAAkB;AAAA,QAClC,gBAAgB,KAAK,SAAS;AAAA,MAChC;AAAA;AAAA,MAGA,oBAAoB,+CAAe;AAAA,MACnC,gBAAgB,+CAAe;AAAA,IACjC;AAEA,UAAM,EAAE,UAAU,kBAAkB,IAClC,oCAAoC,MAAM;AAE5C,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,cAAM,EAAE,OAAO,YAAY,aAAa,IAAI;AAAA,UAC1C;AAAA,WACA,UAAK,SAAS,uBAAd,YAAoC;AAAA,UACpC,KAAK,SAAS;AAAA,UACd,KAAK;AAAA,QACP;AAEA,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA,gBAAgB,KAAK,SAAS;AAAA,YAC9B;AAAA,YACA;AAAA,YACA,eAAe,KAAK,SAAS;AAAA,UAC/B;AAAA,UACA,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;AAAA,QACzC;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,kBAAkB;AAAA,cAChB,GAAG;AAAA,cACH,kBAAkB;AAAA,cAClB,gBACE,KAAK,UAAU;AAAA;AAAA,cAGf,KAAK,4BACD,iCAAiC,KAAK,MAAM,IAC5C;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA,gBAAgB,KAAK,SAAS;AAAA,YAC9B,eAAe,KAAK,SAAS;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA,OAAO;AAAA,cACL,sBAAsB;AAAA,gBACpB;AAAA,kBACE,MAAM,KAAK,KAAK;AAAA,kBAChB,cAAa,UAAK,KAAK,gBAAV,YAAyB;AAAA,kBACtC,YAAY;AAAA,oBACV,KAAK,KAAK;AAAA,kBACZ;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY,EAAE,uBAAuB,EAAE,MAAM,MAAM,EAAE;AAAA,YACrD,gBAAgB,KAAK,SAAS;AAAA,YAC9B,eAAe,KAAK,SAAS;AAAA,UAC/B;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA0B;AAChC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY,KAAmB;AAC7B,WAAO,KAAK,OAAO,eAAe,GAAG;AAAA,EACvC;AAAA,EAEA,MAAM,WACJ,SAC6D;AAhOjE;AAiOI,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AACrD,UAAM,OAAO,KAAK,UAAU,IAAI;AAEhC,UAAM,gBAAgB;AAAA,MACpB,MAAM,QAAQ,KAAK,OAAO,OAAO;AAAA,MACjC,QAAQ;AAAA,IACV;AAEA,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,UAAU;AAAA,IACZ,IAAI,MAAM,cAAc;AAAA,MACtB,KAAK,GAAG,KAAK,OAAO,OAAO,IAAI;AAAA,QAC7B,KAAK;AAAA,MACP,CAAC;AAAA,MACD,SAAS;AAAA,MACT,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,2BAA2B,0BAA0B,cAAc;AAAA,MACnE,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAChD,UAAM,YAAY,SAAS,WAAW,CAAC;AAEvC,UAAM,QACJ,UAAU,WAAW,QACrB,OAAO,UAAU,YAAY,YAC7B,EAAE,WAAW,UAAU,WACnB,CAAC,IACD,UAAU,QAAQ;AAExB,UAAM,YAAY,sBAAsB;AAAA,MACtC;AAAA;AAAA,MACA,YAAY,KAAK,OAAO;AAAA,IAC1B,CAAC;AAED,UAAM,gBAAgB,SAAS;AAE/B,WAAO;AAAA,MACL,MAAM,iBAAiB,KAAK;AAAA,MAC5B,WAAW,6BAA6B,KAAK;AAAA,MAC7C,QAAO,wBAAmB,KAAK,MAAxB,mBAA2B,IAAI,WAAS;AAAA,QAC7C,MAAM,KAAK,WAAW;AAAA,QACtB,UAAU,KAAK,WAAW;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,cAAc,kCAAkC;AAAA,QAC9C,cAAc,UAAU;AAAA,QACxB,cAAc,aAAa,QAAQ,UAAU,SAAS;AAAA,MACxD,CAAC;AAAA,MACD,OAAO;AAAA,QACL,eAAc,oDAAe,qBAAf,YAAmC;AAAA,QACjD,mBAAkB,oDAAe,yBAAf,YAAuC;AAAA,MAC3D;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,iBAAiB,MAAM,YAAY;AAAA,MAC3D;AAAA,MACA,kBAAkB;AAAA,QAChB,QAAQ;AAAA,UACN,oBAAmB,eAAU,sBAAV,YAA+B;AAAA,UAClD,gBAAe,eAAU,kBAAV,YAA2B;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,SAAS,eAAe;AAAA,QACtB,mBAAmB,UAAU;AAAA,QAC7B,YAAY,KAAK,OAAO;AAAA,MAC1B,CAAC;AAAA,MACD,SAAS,EAAE,KAAK;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,UAAM,EAAE,MAAM,SAAS,IAAI,MAAM,KAAK,QAAQ,OAAO;AAErD,UAAM,OAAO,KAAK,UAAU,IAAI;AAChC,UAAM,UAAU;AAAA,MACd,MAAM,QAAQ,KAAK,OAAO,OAAO;AAAA,MACjC,QAAQ;AAAA,IACV;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAM,cAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,IAAI;AAAA,QAC7B,KAAK;AAAA,MACP,CAAC;AAAA,MACD;AAAA,MACA,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,2BAA2B,iCAAiC,WAAW;AAAA,MACvE,aAAa,QAAQ;AAAA,MACrB,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,QAAI,eAA4C;AAChD,QAAI,QAA4D;AAAA,MAC9D,cAAc,OAAO;AAAA,MACrB,kBAAkB,OAAO;AAAA,IAC3B;AACA,QAAI,mBACF;AAEF,UAAMC,cAAa,KAAK,OAAO;AAC/B,QAAI,eAAe;AAEnB,WAAO;AAAA,MACL,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,UAAU,OAAO,YAAY;AArVvC;AAsVY,gBAAI,CAAC,MAAM,SAAS;AAClB,yBAAW,QAAQ,EAAE,MAAM,SAAS,OAAO,MAAM,MAAM,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,kBAAM,gBAAgB,MAAM;AAE5B,gBAAI,iBAAiB,MAAM;AACzB,sBAAQ;AAAA,gBACN,eAAc,mBAAc,qBAAd,YAAkC;AAAA,gBAChD,mBAAkB,mBAAc,yBAAd,YAAsC;AAAA,cAC1D;AAAA,YACF;AAEA,kBAAM,aAAY,WAAM,eAAN,mBAAmB;AAGrC,gBAAI,aAAa,MAAM;AACrB;AAAA,YACF;AAEA,kBAAM,UAAU,UAAU;AAG1B,gBAAI,WAAW,MAAM;AACnB,oBAAM,YAAY,iBAAiB,QAAQ,KAAK;AAChD,kBAAI,aAAa,MAAM;AACrB,2BAAW,QAAQ;AAAA,kBACjB,MAAM;AAAA,kBACN,WAAW;AAAA,gBACb,CAAC;AAAA,cACH;AAEA,oBAAM,qBAAqB;AAAA,gBACzB,QAAQ;AAAA,cACV;AACA,kBAAI,sBAAsB,MAAM;AAC9B,2BAAW,QAAQ,oBAAoB;AACrC,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,WAAW,KAAK;AAAA,kBAClB,CAAC;AAAA,gBACH;AAAA,cACF;AAEA,oBAAM,kBAAkB,mBAAmB,QAAQ,KAAK;AACxD,kBAAI,mBAAmB,MAAM;AAC3B,2BAAW,QAAQ,iBAAiB;AAClC,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,UAAU,KAAK,WAAW;AAAA,oBAC1B,MAAM,KAAK,WAAW;AAAA,kBACxB,CAAC;AAAA,gBACH;AAAA,cACF;AAEA,oBAAM,iBAAiB,sBAAsB;AAAA,gBAC3C,OAAO,QAAQ;AAAA,gBACf,YAAAA;AAAA,cACF,CAAC;AAED,kBAAI,kBAAkB,MAAM;AAC1B,2BAAW,YAAY,gBAAgB;AACrC,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,cAAc;AAAA,oBACd,YAAY,SAAS;AAAA,oBACrB,UAAU,SAAS;AAAA,oBACnB,eAAe,SAAS;AAAA,kBAC1B,CAAC;AAED,6BAAW,QAAQ;AAAA,oBACjB,MAAM;AAAA,oBACN,cAAc;AAAA,oBACd,YAAY,SAAS;AAAA,oBACrB,UAAU,SAAS;AAAA,oBACnB,MAAM,SAAS;AAAA,kBACjB,CAAC;AAED,iCAAe;AAAA,gBACjB;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,UAAU,gBAAgB,MAAM;AAClC,6BAAe,kCAAkC;AAAA,gBAC/C,cAAc,UAAU;AAAA,gBACxB;AAAA,cACF,CAAC;AAED,oBAAM,WACJ,oBAAe;AAAA,gBACb,mBAAmB,UAAU;AAAA,gBAC7B,YAAAA;AAAA,cACF,CAAC,MAHD,YAGM,CAAC;AAET,yBAAW,UAAU,SAAS;AAC5B,2BAAW,QAAQ,EAAE,MAAM,UAAU,OAAO,CAAC;AAAA,cAC/C;AAEA,iCAAmB;AAAA,gBACjB,QAAQ;AAAA,kBACN,oBAAmB,eAAU,sBAAV,YAA+B;AAAA,kBAClD,gBAAe,eAAU,kBAAV,YAA2B;AAAA,gBAC5C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UAEA,MAAM,YAAY;AAChB,uBAAW,QAAQ;AAAA,cACjB,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC;AAAA,MACA,SAAS,EAAE,KAAK;AAAA,IAClB;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB;AAAA,EAC7B;AAAA,EACA,YAAAA;AACF,GAGG;AACD,QAAM,oBAAoB,+BAAO;AAAA,IAC/B,UAAQ,kBAAkB;AAAA;AAO5B,SAAO,qBAAqB,QAAQ,kBAAkB,WAAW,IAC7D,SACA,kBAAkB,IAAI,WAAS;AAAA,IAC7B,cAAc;AAAA,IACd,YAAYA,YAAW;AAAA,IACvB,UAAU,KAAK,aAAa;AAAA,IAC5B,MAAM,KAAK,UAAU,KAAK,aAAa,IAAI;AAAA,EAC7C,EAAE;AACR;AAEA,SAAS,iBAAiB,OAA+C;AACvE,QAAM,YAAY,+BAAO;AAAA,IACvB,UAAQ,UAAU,QAAS,KAAa,YAAY;AAAA;AAGtD,SAAO,aAAa,QAAQ,UAAU,WAAW,IAC7C,SACA,UAAU,IAAI,UAAQ,KAAK,IAAI,EAAE,KAAK,EAAE;AAC9C;AAEA,SAAS,6BACP,OACmD;AACnD,QAAM,iBAAiB,+BAAO;AAAA,IAC5B,UACE,UAAU,QAAS,KAAa,YAAY,QAAQ,KAAK,QAAQ;AAAA;AAKrE,SAAO,kBAAkB,QAAQ,eAAe,WAAW,IACvD,SACA,eAAe,IAAI,WAAS,EAAE,MAAM,QAAQ,MAAM,KAAK,KAAK,EAAE;AACpE;AAEA,SAAS,mBAAmB,OAA+C;AACzE,SAAO,+BAAO;AAAA,IACZ,CACE,SAGG,gBAAgB;AAAA;AAEzB;AAEA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA,YAAAA;AACF,GAGwC;AAzhBxC;AA0hBE,UAAO,4DAAmB,oBAAnB,mBACH;AAAA,IACA,CACE,UAGG,MAAM,OAAO;AAAA,IAEnB,IAAI,YAAU;AAAA,IACb,YAAY;AAAA,IACZ,IAAIA,YAAW;AAAA,IACf,KAAK,MAAM,IAAI;AAAA,IACf,OAAO,MAAM,IAAI;AAAA,EACnB;AACJ;AAEA,IAAM,gBAAgBC,GAAE,OAAO;AAAA,EAC7B,OAAOA,GACJ;AAAA,IACCA,GAAE,MAAM;AAAA;AAAA,MAENA,GAAE,OAAO;AAAA,QACP,cAAcA,GAAE,OAAO;AAAA,UACrB,MAAMA,GAAE,OAAO;AAAA,UACf,MAAMA,GAAE,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAAA,MACDA,GAAE,OAAO;AAAA,QACP,YAAYA,GAAE,OAAO;AAAA,UACnB,UAAUA,GAAE,OAAO;AAAA,UACnB,MAAMA,GAAE,OAAO;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,MACDA,GAAE,OAAO;AAAA,QACP,MAAMA,GAAE,OAAO,EAAE,QAAQ;AAAA,QACzB,SAASA,GAAE,QAAQ,EAAE,QAAQ;AAAA,MAC/B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,EACC,QAAQ;AACb,CAAC;AAID,IAAM,uBAAuBA,GAAE,OAAO;AAAA,EACpC,KAAKA,GAAE,OAAO,EAAE,KAAKA,GAAE,OAAO,GAAG,OAAOA,GAAE,OAAO,EAAE,CAAC,EAAE,QAAQ;AAAA,EAC9D,kBAAkBA,GAAE,OAAO,EAAE,KAAKA,GAAE,OAAO,GAAG,OAAOA,GAAE,OAAO,EAAE,CAAC,EAAE,QAAQ;AAC7E,CAAC;AAEM,IAAM,0BAA0BA,GAAE,OAAO;AAAA,EAC9C,kBAAkBA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,EAC9C,kBAAkBA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,EAC9C,kBAAkBA,GAAE,OAAO,EAAE,iBAAiBA,GAAE,OAAO,EAAE,CAAC,EAAE,QAAQ;AAAA,EACpE,iBAAiBA,GAAE,MAAM,oBAAoB,EAAE,QAAQ;AAAA,EACvD,mBAAmBA,GAChB;AAAA,IACCA,GAAE,OAAO;AAAA,MACP,SAASA,GAAE,OAAO;AAAA,QAChB,YAAYA,GAAE,OAAO,EAAE,QAAQ;AAAA,QAC/B,UAAUA,GAAE,OAAO,EAAE,QAAQ;AAAA,QAC7B,MAAMA,GAAE,OAAO,EAAE,QAAQ;AAAA,MAC3B,CAAC;AAAA,MACD,cAAcA,GAAE,OAAO,EAAE,QAAQ;AAAA,MACjC,uBAAuBA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,MACnD,qBAAqBA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,MACjD,kBAAkBA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,MAC9C,iBAAiBA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACH,EACC,QAAQ;AAAA,EACX,mBAAmBA,GAChB,MAAM;AAAA,IACLA,GAAE,OAAO;AAAA,MACP,0BAA0BA,GAAE,OAAO;AAAA,IACrC,CAAC;AAAA,IACDA,GAAE,OAAO,CAAC,CAAC;AAAA,EACb,CAAC,EACA,QAAQ;AACb,CAAC;AAGM,IAAM,qBAAqBA,GAAE,OAAO;AAAA,EACzC,UAAUA,GAAE,OAAO,EAAE,QAAQ;AAAA,EAC7B,aAAaA,GAAE,OAAO,EAAE,QAAQ;AAAA,EAChC,kBAAkBA,GAAE,OAAO,EAAE,QAAQ;AAAA,EACrC,UAAUA,GAAE,OAAO,EAAE,QAAQ;AAAA,EAC7B,eAAeA,GAAE,OAAO,EAAE,QAAQ;AAAA,EAClC,SAASA,GAAE,QAAQ,EAAE,QAAQ;AAC/B,CAAC;AAED,IAAM,iBAAiBA,GAAE,OAAO;AAAA,EAC9B,YAAYA,GAAE;AAAA,IACZA,GAAE,OAAO;AAAA,MACP,SAAS,cAAc,QAAQ,EAAE,GAAGA,GAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC;AAAA,MACzD,cAAcA,GAAE,OAAO,EAAE,QAAQ;AAAA,MACjC,eAAeA,GAAE,MAAM,kBAAkB,EAAE,QAAQ;AAAA,MACnD,mBAAmB,wBAAwB,QAAQ;AAAA,IACrD,CAAC;AAAA,EACH;AAAA,EACA,eAAeA,GACZ,OAAO;AAAA,IACN,kBAAkBA,GAAE,OAAO,EAAE,QAAQ;AAAA,IACrC,sBAAsBA,GAAE,OAAO,EAAE,QAAQ;AAAA,IACzC,iBAAiBA,GAAE,OAAO,EAAE,QAAQ;AAAA,EACtC,CAAC,EACA,QAAQ;AACb,CAAC;AAID,IAAM,cAAcA,GAAE,OAAO;AAAA,EAC3B,YAAYA,GACT;AAAA,IACCA,GAAE,OAAO;AAAA,MACP,SAAS,cAAc,QAAQ;AAAA,MAC/B,cAAcA,GAAE,OAAO,EAAE,QAAQ;AAAA,MACjC,eAAeA,GAAE,MAAM,kBAAkB,EAAE,QAAQ;AAAA,MACnD,mBAAmB,wBAAwB,QAAQ;AAAA,IACrD,CAAC;AAAA,EACH,EACC,QAAQ;AAAA,EACX,eAAeA,GACZ,OAAO;AAAA,IACN,kBAAkBA,GAAE,OAAO,EAAE,QAAQ;AAAA,IACrC,sBAAsBA,GAAE,OAAO,EAAE,QAAQ;AAAA,IACzC,iBAAiBA,GAAE,OAAO,EAAE,QAAQ;AAAA,EACtC,CAAC,EACA,QAAQ;AACb,CAAC;AAED,IAAM,0CAA0CA,GAAE,OAAO;AAAA,EACvD,oBAAoBA,GAAE,MAAMA,GAAE,KAAK,CAAC,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ;AAAA,EAC/D,gBAAgBA,GACb,OAAO;AAAA,IACN,gBAAgBA,GAAE,OAAO,EAAE,QAAQ;AAAA,IACnC,iBAAiBA,GAAE,QAAQ,EAAE,QAAQ;AAAA,EACvC,CAAC,EACA,QAAQ;AACb,CAAC;;;AOpqBD;AAAA,EAEE;AAAA,OACK;AACP;AAAA,EACE,kBAAAC;AAAA,EACA,6BAAAC;AAAA,EAEA,iBAAAC;AAAA,EACA,WAAAC;AAAA,OACK;AACP,SAAS,KAAAC,UAAS;AAcX,IAAM,mCAAN,MAEP;AAAA,EAmBE,YACE,SACA,UACA,QACA;AAtBF,SAAS,uBAAuB;AAuB9B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EApBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,uBAA+B;AACjC,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,wBAAiC;AACnC,WAAO;AAAA,EACT;AAAA,EAYA,MAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAEE;AACA,QAAI,OAAO,SAAS,KAAK,sBAAsB;AAC7C,YAAM,IAAI,mCAAmC;AAAA,QAC3C,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,QACd,sBAAsB,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,gBAAgBC;AAAA,MACpB,MAAMC,SAAQ,KAAK,OAAO,OAAO;AAAA,MACjC;AAAA,IACF;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,MAAMC,eAAc;AAAA,MAC/D,KAAK,GAAG,KAAK,OAAO,OAAO,WAAW,KAAK,OAAO;AAAA,MAClD,SAAS;AAAA,MACT,MAAM;AAAA,QACJ,UAAU,OAAO,IAAI,YAAU;AAAA,UAC7B,OAAO,UAAU,KAAK,OAAO;AAAA,UAC7B,SAAS,EAAE,MAAM,QAAQ,OAAO,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE;AAAA,UAClD,sBAAsB,KAAK,SAAS;AAAA,UACpC,UAAU,KAAK,SAAS;AAAA,QAC1B,EAAE;AAAA,MACJ;AAAA,MACA,uBAAuB;AAAA,MACvB,2BAA2BC;AAAA,QACzB;AAAA,MACF;AAAA,MACA;AAAA,MACA,OAAO,KAAK,OAAO;AAAA,IACrB,CAAC;AAED,WAAO;AAAA,MACL,YAAY,SAAS,WAAW,IAAI,UAAQ,KAAK,MAAM;AAAA,MACvD,OAAO;AAAA,MACP,aAAa,EAAE,SAAS,gBAAgB;AAAA,IAC1C;AAAA,EACF;AACF;AAIA,IAAM,gDAAgDC,GAAE,OAAO;AAAA,EAC7D,YAAYA,GAAE,MAAMA,GAAE,OAAO,EAAE,QAAQA,GAAE,MAAMA,GAAE,OAAO,CAAC,EAAE,CAAC,CAAC;AAC/D,CAAC;;;AC5GM,SAAS,mBAAmB,KAAmB;AACpD,SAAO,IACJ,SAAS,EACT,WAAW,yDAAyD;AACzE;;;ATkGO,SAAS,yBACd,UAA8C,CAAC,GACnB;AAxG9B;AAyGE,QAAM,WACJ,0BAAqB,QAAQ,OAAO,MAApC,YACA;AAEF,QAAM,aAAa,OAAO;AAAA,IACxB,kBAAkB,WAAW;AAAA,MAC3B,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC;AAAA,IACD,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAAuC,CAAC,MACxC;AAzHJ,QAAAC;AA0HI,eAAI,gCAAgC,SAAS,UAAU;AAAA,MACrD,UAAU;AAAA,MACV;AAAA,MACA,SAAS;AAAA,MACT,aAAYA,MAAA,QAAQ,eAAR,OAAAA,MAAsB;AAAA,MAClC,gBAAgB;AAAA,MAChB,OAAO,QAAQ;AAAA,IACjB,CAAC;AAAA;AAEH,QAAM,uBAAuB,CAC3B,SACA,WAAgD,CAAC,MAEjD,IAAI,iCAAiC,SAAS,UAAU;AAAA,IACtD,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,eAAe;AACxB,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAE9B,SAAO;AACT;AAKO,IAAM,SAAS,yBAAyB;", "names": ["z", "UnsupportedFunctionalityError", "UnsupportedFunctionalityError", "generateId", "z", "combineHeaders", "createJsonResponseHandler", "postJsonToApi", "resolve", "z", "combineHeaders", "resolve", "postJsonToApi", "createJsonResponseHandler", "z", "_a"]}