{"version": 3, "file": "BetaMessageStream.d.ts", "sourceRoot": "", "sources": ["../src/lib/BetaMessageStream.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5E,OAAO,EACL,KAAK,gBAAgB,EACrB,QAAQ,IAAI,YAAY,EACxB,KAAK,WAAW,EAChB,KAAK,yBAAyB,IAAI,sBAAsB,EACxD,KAAK,gBAAgB,EACrB,KAAK,mBAAmB,IAAI,uBAAuB,EACnD,KAAK,uBAAuB,IAAI,2BAA2B,EAE3D,KAAK,gBAAgB,EACtB,MAAM,oDAAoD,CAAC;AAC5D,OAAO,EAAE,KAAK,cAAc,EAAE,KAAK,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AAIpF,MAAM,WAAW,mBAAmB;IAClC,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,WAAW,EAAE,CAAC,KAAK,EAAE,sBAAsB,EAAE,QAAQ,EAAE,WAAW,KAAK,IAAI,CAAC;IAC5E,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,KAAK,IAAI,CAAC;IACxD,QAAQ,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,KAAK,IAAI,CAAC;IACtF,SAAS,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,CAAC;IAChE,QAAQ,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,KAAK,IAAI,CAAC;IACpE,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,IAAI,CAAC;IACvC,OAAO,EAAE,CAAC,OAAO,EAAE,WAAW,KAAK,IAAI,CAAC;IACxC,YAAY,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAClD,YAAY,EAAE,CAAC,OAAO,EAAE,WAAW,KAAK,IAAI,CAAC;IAC7C,KAAK,EAAE,CAAC,KAAK,EAAE,cAAc,KAAK,IAAI,CAAC;IACvC,KAAK,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC1C,GAAG,EAAE,MAAM,IAAI,CAAC;CACjB;AASD,qBAAa,iBAAkB,YAAW,aAAa,CAAC,sBAAsB,CAAC;;IAC7E,QAAQ,EAAE,gBAAgB,EAAE,CAAM;IAClC,gBAAgB,EAAE,WAAW,EAAE,CAAM;IAGrC,UAAU,EAAE,eAAe,CAAyB;;IAsCpD,IAAI,QAAQ,IAAI,QAAQ,GAAG,IAAI,GAAG,SAAS,CAE1C;IAED,IAAI,UAAU,IAAI,MAAM,GAAG,IAAI,GAAG,SAAS,CAE1C;IAED;;;;;;;;;OASG;IACG,YAAY,IAAI,OAAO,CAAC;QAC5B,IAAI,EAAE,iBAAiB,CAAC;QACxB,QAAQ,EAAE,QAAQ,CAAC;QACnB,UAAU,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;KACvC,CAAC;IAaF;;;;;;OAMG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,GAAG,iBAAiB;IAMpE,MAAM,CAAC,aAAa,CAClB,QAAQ,EAAE,YAAY,EACtB,MAAM,EAAE,2BAA2B,EACnC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,iBAAiB;IAepB,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC;IAO3C,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,gBAAgB;IAIpD,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,UAAO;cAOvC,cAAc,CAC5B,QAAQ,EAAE,YAAY,EACtB,MAAM,EAAE,uBAAuB,EAC/B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,IAAI,CAAC;IAoBhB,SAAS,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAQ9C,IAAI,KAAK,IAAI,OAAO,CAEnB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,KAAK;IAIL;;;;;;OAMG;IACH,EAAE,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI;IAOrG;;;;;;OAMG;IACH,GAAG,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI;IAQtG;;;;OAIG;IACH,IAAI,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI;IAOvG;;;;;;;;;;OAUG;IACH,OAAO,CAAC,KAAK,SAAS,MAAM,mBAAmB,EAC7C,KAAK,EAAE,KAAK,GACX,OAAO,CACR,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,GAClE,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,GAAG,IAAI,GACxD,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CACzC;IAQK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAK3B,IAAI,cAAc,IAAI,WAAW,GAAG,SAAS,CAE5C;IASD;;;OAGG;IACG,YAAY,IAAI,OAAO,CAAC,WAAW,CAAC;IAmB1C;;;;OAIG;IACG,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IA0BlC,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,MAAM,mBAAmB,EACrD,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IA8CjD,SAAS,CAAC,UAAU;cAqFJ,mBAAmB,CACjC,cAAc,EAAE,cAAc,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,IAAI,CAAC;IA2GhB,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,sBAAsB,CAAC;IA6D/D,gBAAgB,IAAI,cAAc;CAInC"}