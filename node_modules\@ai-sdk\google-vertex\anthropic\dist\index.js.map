{"version": 3, "sources": ["../../src/anthropic/index.ts", "../../src/anthropic/google-vertex-anthropic-provider-node.ts", "../../src/google-vertex-auth-google-auth-library.ts", "../../src/anthropic/google-vertex-anthropic-provider.ts"], "sourcesContent": ["export {\n  vertexAnthropic,\n  createVertexAnthropic,\n} from './google-vertex-anthropic-provider-node';\nexport type {\n  GoogleVertexAnthropicProvider,\n  GoogleVertexAnthropicProviderSettings,\n} from './google-vertex-anthropic-provider-node';\n", "import { resolve } from '@ai-sdk/provider-utils';\nimport { GoogleAuthOptions } from 'google-auth-library';\nimport { generateAuthToken } from '../google-vertex-auth-google-auth-library';\nimport {\n  createVertexAnthropic as createVertexAnthropicOriginal,\n  GoogleVertexAnthropicProvider,\n  GoogleVertexAnthropicProviderSettings as GoogleVertexAnthropicProviderSettingsOriginal,\n} from './google-vertex-anthropic-provider';\n\nexport type { GoogleVertexAnthropicProvider };\n\nexport interface GoogleVertexAnthropicProviderSettings\n  extends GoogleVertexAnthropicProviderSettingsOriginal {\n  /**\n Optional. The Authentication options provided by google-auth-library.\nComplete list of authentication options is documented in the\nGoogleAuthOptions interface:\nhttps://github.com/googleapis/google-auth-library-nodejs/blob/main/src/auth/googleauth.ts.\n   */\n  googleAuthOptions?: GoogleAuthOptions;\n}\n\nexport function createVertexAnthropic(\n  options: GoogleVertexAnthropicProviderSettings = {},\n): GoogleVertexAnthropicProvider {\n  return createVertexAnthropicOriginal({\n    ...options,\n    headers: async () => ({\n      Authorization: `Bearer ${await generateAuthToken(\n        options.googleAuthOptions,\n      )}`,\n      ...(await resolve(options.headers)),\n    }),\n  });\n}\n\n/**\nDefault Google Vertex Anthropic provider instance.\n */\nexport const vertexAnthropic = createVertexAnthropic();\n", "import { GoogleAuth, GoogleAuthOptions } from 'google-auth-library';\n\nlet authInstance: GoogleAuth | null = null;\nlet authOptions: GoogleAuthOptions | null = null;\n\nfunction getAuth(options: GoogleAuthOptions) {\n  if (!authInstance || options !== authOptions) {\n    authInstance = new GoogleAuth({\n      scopes: ['https://www.googleapis.com/auth/cloud-platform'],\n      ...options,\n    });\n    authOptions = options;\n  }\n  return authInstance;\n}\n\nexport async function generateAuthToken(options?: GoogleAuthOptions) {\n  const auth = getAuth(options || {});\n  const client = await auth.getClient();\n  const token = await client.getAccessToken();\n  return token?.token || null;\n}\n\n// For testing purposes only\nexport function _resetAuthInstance() {\n  authInstance = null;\n}\n", "import {\n  LanguageModelV1,\n  NoSuchModelError,\n  ProviderV1,\n} from '@ai-sdk/provider';\nimport {\n  FetchFunction,\n  Resolvable,\n  loadOptionalSetting,\n  withoutTrailingSlash,\n} from '@ai-sdk/provider-utils';\nimport {\n  anthropicTools,\n  AnthropicMessagesLanguageModel,\n  AnthropicMessagesModelId,\n} from '@ai-sdk/anthropic/internal';\nimport {\n  GoogleVertexAnthropicMessagesModelId,\n  GoogleVertexAnthropicMessagesSettings,\n} from './google-vertex-anthropic-messages-settings';\nexport interface GoogleVertexAnthropicProvider extends ProviderV1 {\n  /**\nCreates a model for text generation.\n*/\n  (\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings?: GoogleVertexAnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates a model for text generation.\n*/\n  languageModel(\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings?: GoogleVertexAnthropicMessagesSettings,\n  ): LanguageModelV1;\n\n  /**\nAnthropic-specific computer use tool.\n   */\n  tools: typeof anthropicTools;\n}\n\nexport interface GoogleVertexAnthropicProviderSettings {\n  /**\n   * Google Cloud project ID. Defaults to the value of the `GOOGLE_VERTEX_PROJECT` environment variable.\n   */\n  project?: string;\n\n  /**\n   * Google Cloud region. Defaults to the value of the `GOOGLE_VERTEX_LOCATION` environment variable.\n   */\n  location?: string;\n\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers.\nThe default prefix is `https://api.anthropic.com/v1`.\n   */\n  baseURL?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Resolvable<Record<string, string | undefined>>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n}\n\n/**\nCreate a Google Vertex Anthropic provider instance.\n */\nexport function createVertexAnthropic(\n  options: GoogleVertexAnthropicProviderSettings = {},\n): GoogleVertexAnthropicProvider {\n  const location = loadOptionalSetting({\n    settingValue: options.location,\n    environmentVariableName: 'GOOGLE_VERTEX_LOCATION',\n  });\n  const project = loadOptionalSetting({\n    settingValue: options.project,\n    environmentVariableName: 'GOOGLE_VERTEX_PROJECT',\n  });\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ??\n    `https://${location}-aiplatform.googleapis.com/v1/projects/${project}/locations/${location}/publishers/anthropic/models`;\n\n  const createChatModel = (\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings: GoogleVertexAnthropicMessagesSettings = {},\n  ) =>\n    new AnthropicMessagesLanguageModel(\n      modelId as AnthropicMessagesModelId,\n      settings,\n      {\n        provider: 'vertex.anthropic.messages',\n        baseURL,\n        headers: options.headers ?? {},\n        fetch: options.fetch,\n        supportsImageUrls: false,\n        buildRequestUrl: (baseURL, isStreaming) =>\n          `${baseURL}/${modelId}:${\n            isStreaming ? 'streamRawPredict' : 'rawPredict'\n          }`,\n        transformRequestBody: args => {\n          // Remove model from args and add anthropic version\n          const { model, ...rest } = args;\n          return {\n            ...rest,\n            anthropic_version: 'vertex-2023-10-16',\n          };\n        },\n      },\n    );\n\n  const provider = function (\n    modelId: GoogleVertexAnthropicMessagesModelId,\n    settings?: GoogleVertexAnthropicMessagesSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Anthropic model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(modelId, settings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.messages = createChatModel;\n  provider.textEmbeddingModel = (modelId: string) => {\n    throw new NoSuchModelError({ modelId, modelType: 'textEmbeddingModel' });\n  };\n\n  provider.tools = anthropicTools;\n\n  return provider;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,+BAAAA;AAAA,EAAA;AAAA;AAAA;;;ACAA,IAAAC,yBAAwB;;;ACAxB,iCAA8C;AAE9C,IAAI,eAAkC;AACtC,IAAI,cAAwC;AAE5C,SAAS,QAAQ,SAA4B;AAC3C,MAAI,CAAC,gBAAgB,YAAY,aAAa;AAC5C,mBAAe,IAAI,sCAAW;AAAA,MAC5B,QAAQ,CAAC,gDAAgD;AAAA,MACzD,GAAG;AAAA,IACL,CAAC;AACD,kBAAc;AAAA,EAChB;AACA,SAAO;AACT;AAEA,eAAsB,kBAAkB,SAA6B;AACnE,QAAM,OAAO,QAAQ,WAAW,CAAC,CAAC;AAClC,QAAM,SAAS,MAAM,KAAK,UAAU;AACpC,QAAM,QAAQ,MAAM,OAAO,eAAe;AAC1C,UAAO,+BAAO,UAAS;AACzB;;;ACrBA,sBAIO;AACP,4BAKO;AACP,sBAIO;AA4DA,SAAS,sBACd,UAAiD,CAAC,GACnB;AA7EjC;AA8EE,QAAM,eAAW,2CAAoB;AAAA,IACnC,cAAc,QAAQ;AAAA,IACtB,yBAAyB;AAAA,EAC3B,CAAC;AACD,QAAM,cAAU,2CAAoB;AAAA,IAClC,cAAc,QAAQ;AAAA,IACtB,yBAAyB;AAAA,EAC3B,CAAC;AACD,QAAM,WACJ,qDAAqB,QAAQ,OAAO,MAApC,YACA,WAAW,QAAQ,0CAA0C,OAAO,cAAc,QAAQ;AAE5F,QAAM,kBAAkB,CACtB,SACA,WAAkD,CAAC,MACnD;AA7FJ,QAAAC;AA8FI,eAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,QACE,UAAU;AAAA,QACV;AAAA,QACA,UAASA,MAAA,QAAQ,YAAR,OAAAA,MAAmB,CAAC;AAAA,QAC7B,OAAO,QAAQ;AAAA,QACf,mBAAmB;AAAA,QACnB,iBAAiB,CAACC,UAAS,gBACzB,GAAGA,QAAO,IAAI,OAAO,IACnB,cAAc,qBAAqB,YACrC;AAAA,QACF,sBAAsB,UAAQ;AAE5B,gBAAM,EAAE,OAAO,GAAG,KAAK,IAAI;AAC3B,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAEF,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,WAAW;AACpB,WAAS,qBAAqB,CAAC,YAAoB;AACjD,UAAM,IAAI,iCAAiB,EAAE,SAAS,WAAW,qBAAqB,CAAC;AAAA,EACzE;AAEA,WAAS,QAAQ;AAEjB,SAAO;AACT;;;AFvHO,SAASC,uBACd,UAAiD,CAAC,GACnB;AAC/B,SAAO,sBAA8B;AAAA,IACnC,GAAG;AAAA,IACH,SAAS,aAAa;AAAA,MACpB,eAAe,UAAU,MAAM;AAAA,QAC7B,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,GAAI,UAAM,gCAAQ,QAAQ,OAAO;AAAA,IACnC;AAAA,EACF,CAAC;AACH;AAKO,IAAM,kBAAkBA,uBAAsB;", "names": ["createVertexAnthropic", "import_provider_utils", "_a", "baseURL", "createVertexAnthropic"]}