{"version": 3, "sources": ["../src/index.ts", "../src/azure-openai-provider.ts"], "sourcesContent": ["export { azure, createAzure } from './azure-openai-provider';\nexport type {\n  AzureOpenAIProvider,\n  AzureOpenAIProviderSettings,\n} from './azure-openai-provider';\n", "import {\n  OpenAIChatLanguageModel,\n  OpenAIChatSettings,\n  OpenAICompletionLanguageModel,\n  OpenAICompletionSettings,\n  OpenAIEmbeddingModel,\n  OpenAIEmbeddingSettings,\n  OpenAIImageModel,\n  OpenAIImageSettings,\n  OpenAIResponsesLanguageModel,\n  OpenAITranscriptionModel,\n} from '@ai-sdk/openai/internal';\nimport {\n  EmbeddingModelV1,\n  LanguageModelV1,\n  ProviderV1,\n  ImageModelV1,\n  TranscriptionModelV1,\n} from '@ai-sdk/provider';\nimport { FetchFunction, loadApiKey, loadSetting } from '@ai-sdk/provider-utils';\n\nexport interface AzureOpenAIProvider extends ProviderV1 {\n  (deploymentId: string, settings?: OpenAIChatSettings): LanguageModelV1;\n\n  /**\nCreates an Azure OpenAI chat model for text generation.\n   */\n  languageModel(\n    deploymentId: string,\n    settings?: OpenAIChatSettings,\n  ): LanguageModelV1;\n\n  /**\nCreates an Azure OpenAI chat model for text generation.\n   */\n  chat(deploymentId: string, settings?: OpenAIChatSettings): LanguageModelV1;\n\n  /**\nCreates an Azure OpenAI responses API model for text generation.\n   */\n  responses(deploymentId: string): LanguageModelV1;\n\n  /**\nCreates an Azure OpenAI completion model for text generation.\n   */\n  completion(\n    deploymentId: string,\n    settings?: OpenAICompletionSettings,\n  ): LanguageModelV1;\n\n  /**\n@deprecated Use `textEmbeddingModel` instead.\n   */\n  embedding(\n    deploymentId: string,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\n   * Creates an Azure OpenAI DALL-E model for image generation.\n   * @deprecated Use `imageModel` instead.\n   */\n  image(deploymentId: string, settings?: OpenAIImageSettings): ImageModelV1;\n\n  /**\n   * Creates an Azure OpenAI DALL-E model for image generation.\n   */\n  imageModel(\n    deploymentId: string,\n    settings?: OpenAIImageSettings,\n  ): ImageModelV1;\n\n  /**\n@deprecated Use `textEmbeddingModel` instead.\n   */\n  textEmbedding(\n    deploymentId: string,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\nCreates an Azure OpenAI model for text embeddings.\n   */\n  textEmbeddingModel(\n    deploymentId: string,\n    settings?: OpenAIEmbeddingSettings,\n  ): EmbeddingModelV1<string>;\n\n  /**\n   * Creates an Azure OpenAI model for audio transcription.\n   */\n  transcription(deploymentId: string): TranscriptionModelV1;\n}\n\nexport interface AzureOpenAIProviderSettings {\n  /**\nName of the Azure OpenAI resource. Either this or `baseURL` can be used.\n\nThe resource name is used in the assembled URL: `https://{resourceName}.openai.azure.com/openai/deployments/{modelId}{path}`.\n     */\n  resourceName?: string;\n\n  /**\nUse a different URL prefix for API calls, e.g. to use proxy servers. Either this or `resourceName` can be used.\nWhen a baseURL is provided, the resourceName is ignored.\n\nWith a baseURL, the resolved URL is `{baseURL}/{modelId}{path}`.\n   */\n  baseURL?: string;\n\n  /**\nAPI key for authenticating requests.\n     */\n  apiKey?: string;\n\n  /**\nCustom headers to include in the requests.\n     */\n  headers?: Record<string, string>;\n\n  /**\nCustom fetch implementation. You can use it as a middleware to intercept requests,\nor to provide a custom fetch implementation for e.g. testing.\n    */\n  fetch?: FetchFunction;\n\n  /**\nCustom api version to use. Defaults to `2024-10-01-preview`.\n    */\n  apiVersion?: string;\n}\n\n/**\nCreate an Azure OpenAI provider instance.\n */\nexport function createAzure(\n  options: AzureOpenAIProviderSettings = {},\n): AzureOpenAIProvider {\n  const getHeaders = () => ({\n    'api-key': loadApiKey({\n      apiKey: options.apiKey,\n      environmentVariableName: 'AZURE_API_KEY',\n      description: 'Azure OpenAI',\n    }),\n    ...options.headers,\n  });\n\n  const getResourceName = () =>\n    loadSetting({\n      settingValue: options.resourceName,\n      settingName: 'resourceName',\n      environmentVariableName: 'AZURE_RESOURCE_NAME',\n      description: 'Azure OpenAI resource name',\n    });\n\n  const apiVersion = options.apiVersion ?? '2025-03-01-preview';\n  const url = ({ path, modelId }: { path: string; modelId: string }) => {\n    if (path === '/responses') {\n      return options.baseURL\n        ? `${options.baseURL}${path}?api-version=${apiVersion}`\n        : `https://${getResourceName()}.openai.azure.com/openai/responses?api-version=${apiVersion}`;\n    }\n\n    // Default URL format for other endpoints\n    return options.baseURL\n      ? `${options.baseURL}/${modelId}${path}?api-version=${apiVersion}`\n      : `https://${getResourceName()}.openai.azure.com/openai/deployments/${modelId}${path}?api-version=${apiVersion}`;\n  };\n\n  const createChatModel = (\n    deploymentName: string,\n    settings: OpenAIChatSettings = {},\n  ) =>\n    new OpenAIChatLanguageModel(deploymentName, settings, {\n      provider: 'azure-openai.chat',\n      url,\n      headers: getHeaders,\n      compatibility: 'strict',\n      fetch: options.fetch,\n    });\n\n  const createCompletionModel = (\n    modelId: string,\n    settings: OpenAICompletionSettings = {},\n  ) =>\n    new OpenAICompletionLanguageModel(modelId, settings, {\n      provider: 'azure-openai.completion',\n      url,\n      compatibility: 'strict',\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createEmbeddingModel = (\n    modelId: string,\n    settings: OpenAIEmbeddingSettings = {},\n  ) =>\n    new OpenAIEmbeddingModel(modelId, settings, {\n      provider: 'azure-openai.embeddings',\n      headers: getHeaders,\n      url,\n      fetch: options.fetch,\n    });\n\n  const createResponsesModel = (modelId: string) =>\n    new OpenAIResponsesLanguageModel(modelId, {\n      provider: 'azure-openai.responses',\n      url,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createImageModel = (\n    modelId: string,\n    settings: OpenAIImageSettings = {},\n  ) =>\n    new OpenAIImageModel(modelId, settings, {\n      provider: 'azure-openai.image',\n      url,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const createTranscriptionModel = (modelId: string) =>\n    new OpenAITranscriptionModel(modelId, {\n      provider: 'azure-openai.transcription',\n      url,\n      headers: getHeaders,\n      fetch: options.fetch,\n    });\n\n  const provider = function (\n    deploymentId: string,\n    settings?: OpenAIChatSettings | OpenAICompletionSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Azure OpenAI model function cannot be called with the new keyword.',\n      );\n    }\n\n    return createChatModel(deploymentId, settings as OpenAIChatSettings);\n  };\n\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n  provider.embedding = createEmbeddingModel;\n  provider.image = createImageModel;\n  provider.imageModel = createImageModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  provider.responses = createResponsesModel;\n  provider.transcription = createTranscriptionModel;\n  return provider;\n}\n\n/**\nDefault Azure OpenAI provider instance.\n */\nexport const azure = createAzure();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,sBAWO;AAQP,4BAAuD;AAoHhD,SAAS,YACd,UAAuC,CAAC,GACnB;AAzIvB;AA0IE,QAAM,aAAa,OAAO;AAAA,IACxB,eAAW,kCAAW;AAAA,MACpB,QAAQ,QAAQ;AAAA,MAChB,yBAAyB;AAAA,MACzB,aAAa;AAAA,IACf,CAAC;AAAA,IACD,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,kBAAkB,UACtB,mCAAY;AAAA,IACV,cAAc,QAAQ;AAAA,IACtB,aAAa;AAAA,IACb,yBAAyB;AAAA,IACzB,aAAa;AAAA,EACf,CAAC;AAEH,QAAM,cAAa,aAAQ,eAAR,YAAsB;AACzC,QAAM,MAAM,CAAC,EAAE,MAAM,QAAQ,MAAyC;AACpE,QAAI,SAAS,cAAc;AACzB,aAAO,QAAQ,UACX,GAAG,QAAQ,OAAO,GAAG,IAAI,gBAAgB,UAAU,KACnD,WAAW,gBAAgB,CAAC,kDAAkD,UAAU;AAAA,IAC9F;AAGA,WAAO,QAAQ,UACX,GAAG,QAAQ,OAAO,IAAI,OAAO,GAAG,IAAI,gBAAgB,UAAU,KAC9D,WAAW,gBAAgB,CAAC,wCAAwC,OAAO,GAAG,IAAI,gBAAgB,UAAU;AAAA,EAClH;AAEA,QAAM,kBAAkB,CACtB,gBACA,WAA+B,CAAC,MAEhC,IAAI,wCAAwB,gBAAgB,UAAU;AAAA,IACpD,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,eAAe;AAAA,IACf,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,wBAAwB,CAC5B,SACA,WAAqC,CAAC,MAEtC,IAAI,8CAA8B,SAAS,UAAU;AAAA,IACnD,UAAU;AAAA,IACV;AAAA,IACA,eAAe;AAAA,IACf,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,uBAAuB,CAC3B,SACA,WAAoC,CAAC,MAErC,IAAI,qCAAqB,SAAS,UAAU;AAAA,IAC1C,UAAU;AAAA,IACV,SAAS;AAAA,IACT;AAAA,IACA,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,uBAAuB,CAAC,YAC5B,IAAI,6CAA6B,SAAS;AAAA,IACxC,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,mBAAmB,CACvB,SACA,WAAgC,CAAC,MAEjC,IAAI,iCAAiB,SAAS,UAAU;AAAA,IACtC,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,2BAA2B,CAAC,YAChC,IAAI,yCAAyB,SAAS;AAAA,IACpC,UAAU;AAAA,IACV;AAAA,IACA,SAAS;AAAA,IACT,OAAO,QAAQ;AAAA,EACjB,CAAC;AAEH,QAAM,WAAW,SACf,cACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,cAAc,QAA8B;AAAA,EACrE;AAEA,WAAS,gBAAgB;AACzB,WAAS,OAAO;AAChB,WAAS,aAAa;AACtB,WAAS,YAAY;AACrB,WAAS,QAAQ;AACjB,WAAS,aAAa;AACtB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAC9B,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,SAAO;AACT;AAKO,IAAM,QAAQ,YAAY;", "names": []}