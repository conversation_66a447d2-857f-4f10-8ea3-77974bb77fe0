import { ProviderV1, LanguageModelV1 } from '@ai-sdk/provider';
import { Resolvable, FetchFunction } from '@ai-sdk/provider-utils';
import { AnthropicMessagesSettings, anthropicTools } from '@ai-sdk/anthropic/internal';

interface GoogleCredentials {
    /**
     * The client email for the Google Cloud service account. Defaults to the
     * value of the `GOOGLE_CLIENT_EMAIL` environment variable.
     */
    clientEmail: string;
    /**
     * The private key for the Google Cloud service account. Defaults to the
     * value of the `GOOGLE_PRIVATE_KEY` environment variable.
     */
    privateKey: string;
    /**
     * Optional. The private key ID for the Google Cloud service account. Defaults
     * to the value of the `GOOGLE_PRIVATE_KEY_ID` environment variable.
     */
    privateKeyId?: string;
}

type GoogleVertexAnthropicMessagesModelId = 'claude-3-7-sonnet@********' | 'claude-3-5-sonnet-v2@********' | 'claude-3-5-haiku@********' | 'claude-3-5-sonnet@********' | 'claude-3-haiku@********' | 'claude-3-sonnet@********' | 'claude-3-opus@********' | (string & {});
interface GoogleVertexAnthropicMessagesSettings extends AnthropicMessagesSettings {
}

interface GoogleVertexAnthropicProvider extends ProviderV1 {
    /**
  Creates a model for text generation.
  */
    (modelId: GoogleVertexAnthropicMessagesModelId, settings?: GoogleVertexAnthropicMessagesSettings): LanguageModelV1;
    /**
  Creates a model for text generation.
  */
    languageModel(modelId: GoogleVertexAnthropicMessagesModelId, settings?: GoogleVertexAnthropicMessagesSettings): LanguageModelV1;
    /**
  Anthropic-specific computer use tool.
     */
    tools: typeof anthropicTools;
}
interface GoogleVertexAnthropicProviderSettings$1 {
    /**
     * Google Cloud project ID. Defaults to the value of the `GOOGLE_VERTEX_PROJECT` environment variable.
     */
    project?: string;
    /**
     * Google Cloud region. Defaults to the value of the `GOOGLE_VERTEX_LOCATION` environment variable.
     */
    location?: string;
    /**
  Use a different URL prefix for API calls, e.g. to use proxy servers.
  The default prefix is `https://api.anthropic.com/v1`.
     */
    baseURL?: string;
    /**
  Custom headers to include in the requests.
       */
    headers?: Resolvable<Record<string, string | undefined>>;
    /**
  Custom fetch implementation. You can use it as a middleware to intercept requests,
  or to provide a custom fetch implementation for e.g. testing.
      */
    fetch?: FetchFunction;
}

interface GoogleVertexAnthropicProviderSettings extends GoogleVertexAnthropicProviderSettings$1 {
    /**
     * Optional. The Google credentials for the Google Cloud service account. If
     * not provided, the Google Vertex provider will use environment variables to
     * load the credentials.
     */
    googleCredentials?: GoogleCredentials;
}
declare function createVertexAnthropic(options?: GoogleVertexAnthropicProviderSettings): GoogleVertexAnthropicProvider;
/**
 * Default Google Vertex AI Anthropic provider instance.
 */
declare const vertexAnthropic: GoogleVertexAnthropicProvider;

export { type GoogleVertexAnthropicProvider, type GoogleVertexAnthropicProviderSettings, createVertexAnthropic, vertexAnthropic };
